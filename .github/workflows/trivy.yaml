name: Trivy scanner

on:
  workflow_dispatch:
  push:
    branches: [ master, 'release-**' ]
    paths:
      - '.github/workflows/trivy.yaml'
      - 'pkg/**.go'
      - '!kubetest2-aks/**.go'
      - 'cmd/**.go'
      - 'go.*'
      - '!vendor/**'
      - 'health-probe-proxy/**'
  pull_request:
    branches: [ master, 'release-**' ]
    paths:
      - '.github/workflows/trivy.yaml'
      - 'pkg/**.go'
      - '!kubetest2-aks/**.go'
      - 'cmd/**.go'
      - 'go.*'
      - '!vendor/**'
      - 'health-probe-proxy/**'
  schedule:
    - cron: '0 1 * * *'
permissions:
  contents: read
jobs:
  build:
    permissions:
      contents: write # for sbom
      security-events: write # for github/codeql-action/upload-sarif to upload SARIF results
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Go 1.x
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'go.mod'
          check-latest: true
          cache-dependency-path: |
            go.sum
        id: go
      - name: Build images
        run: |
          export TAG=${{ github.sha }}
          export IMAGE_TAG=${{ github.sha }}
          make build-ccm-image
          make build-node-image-linux-amd64
          cd health-probe-proxy && make build-health-probe-proxy-image && cd ..

      - name: Run Trivy scanner CCM
        uses: aquasecurity/trivy-action@76071ef0d7ec797419534a183b498b4d6366cf37 # master
        with:
          image-ref: 'local/azure-cloud-controller-manager:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-ccm-results.sarif'
          ignore-unfixed: true
          vuln-type: 'os,library'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW,UNKNOWN'
        env:
          TRIVY_SKIP_DB_UPDATE: true
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@fca7ace96b7d713c7035871441bd52efbe39e27e # v3.28.19
        with:
          sarif_file: 'trivy-ccm-results.sarif'
          category: azure-cloud-controller-manager-image
      - name: Run Trivy scanner CNM
        uses: aquasecurity/trivy-action@76071ef0d7ec797419534a183b498b4d6366cf37 # master
        with:
          image-ref: 'local/azure-cloud-node-manager:${{ github.sha }}-linux-amd64'
          format: 'sarif'
          output: 'trivy-cnm-linux-results.sarif'
          ignore-unfixed: true
          vuln-type: 'os,library'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW,UNKNOWN'
        env:
          TRIVY_SKIP_DB_UPDATE: true
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@fca7ace96b7d713c7035871441bd52efbe39e27e # v3.28.19
        with:
          sarif_file: 'trivy-cnm-linux-results.sarif'
          category: azure-cloud-node-manager-linux-image
      - name: Run Trivy scanner health-probe-proxy
        uses: aquasecurity/trivy-action@76071ef0d7ec797419534a183b498b4d6366cf37 # master
        with:
          image-ref: 'local/health-probe-proxy:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-health-probe-proxy-linux-results.sarif'
          ignore-unfixed: true
          vuln-type: 'os,library'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW,UNKNOWN'
        env:
          TRIVY_SKIP_DB_UPDATE: true
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@fca7ace96b7d713c7035871441bd52efbe39e27e # v3.28.19
        with:
          sarif_file: 'trivy-health-probe-proxy-linux-results.sarif'
          category: health-probe-proxy-linux-image

      - name: Run Trivy vulnerability scanner in repo mode
        uses: aquasecurity/trivy-action@76071ef0d7ec797419534a183b498b4d6366cf37 # master
        with:
          scan-type: 'fs'
          format: 'github'
          output: 'dependency-results.sbom.json'
          scan-ref: '.'
          github-pat: ${{ secrets.GITHUB_TOKEN }}
        env:
          TRIVY_SKIP_DB_UPDATE: true
