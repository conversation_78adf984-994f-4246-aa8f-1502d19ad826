name: Code Lint - azclient

on: 
  push:
    branches: [ master, 'release-**' ]
    paths:
      - 'pkg/azclient/**'
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ master, 'release-**' ]
    paths:
      - 'pkg/azclient/**'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  Lint:
    permissions:
      contents: read  # for actions/checkout to fetch code
      pull-requests: read  # for golangci/golangci-lint-action to fetch pull requests
      checks: write
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'pkg/azclient/go.mod'
          check-latest: true
          cache-dependency-path: |
            pkg/azclient/go.sum
      - name: golangci-lint
        uses: golangci/golangci-lint-action@55c2c1448f86e01eaae002a5a3a9624417608d84 # v6.5.2
        with:
          # Optional: version of golangci-lint to use in form of v1.2 or v1.2.3 or `latest` to use the latest version
          version: v1.64
          args: -v
          verify: true
