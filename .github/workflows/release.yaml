name: Release

on:
  workflow_dispatch:
  push:
    tags:
      - "v*.*.*"

permissions:
  contents: read

jobs:
  build-cloud-controller-manager:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - os: linux
            arch: amd64
          - os: linux
            arch: arm
          - os: linux
            arch: arm64
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit
      - name: Install arm build toolchains
        run: |
          sudo apt-get update
          sudo apt-get install -y gcc-arm-linux-gnueabihf gcc-aarch64-linux-gnu
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: go.mod
      - name: Build binary
        run: |
          rm -rf ./bin
          ARCH=${{ matrix.arch }} make bin/azure-cloud-controller-manager
          mv bin/azure-cloud-controller-manager bin/azure-cloud-controller-manager-${{ matrix.os }}-${{ matrix.arch }}
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: cloud-controller-manager-${{ matrix.os }}-${{ matrix.arch }}
          path: bin/azure-cloud-controller-manager-${{ matrix.os }}-${{ matrix.arch }}
          if-no-files-found: error

  build-cloud-node-manager:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - os: linux
            arch: amd64
          - os: linux
            arch: arm
          - os: linux
            arch: arm64
          - os: windows
            arch: amd64
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit
      - name: Install arm build toolchains
        run: |
          sudo apt-get update
          sudo apt-get install gcc-arm-linux-gnueabihf gcc-aarch64-linux-gnu
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'go.mod'
          check-latest: true
          cache-dependency-path: |
            go.sum
      - name: Build binary for linux
        if: matrix.os == 'linux'
        run: |
          rm -rf ./bin
          ARCH=${{ matrix.arch }} make bin/azure-cloud-node-manager
          mv bin/azure-cloud-node-manager bin/azure-cloud-node-manager-${{ matrix.os }}-${{ matrix.arch }}
      - name: Build binary for windows
        if: matrix.os == 'windows'
        run: |
          rm -rf ./bin
          ARCH=${{ matrix.arch }} make bin/azure-cloud-node-manager.exe
          mv bin/azure-cloud-node-manager-${{ matrix.arch }}.exe bin/azure-cloud-node-manager-${{ matrix.os }}-${{ matrix.arch }}.exe
      - name: Upload artifact for linux
        if: matrix.os == 'linux'
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: cloud-node-manager-${{ matrix.os }}-${{ matrix.arch }}
          path: bin/azure-cloud-node-manager-${{ matrix.os }}-${{ matrix.arch }}
      - name: Upload artifact for windows
        if: matrix.os == 'windows'
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: cloud-node-manager-${{ matrix.os }}-${{ matrix.arch }}
          path: bin/azure-cloud-node-manager-${{ matrix.os }}-${{ matrix.arch }}.exe
          if-no-files-found: error

  build-acr-credential-provider:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - os: linux
            arch: amd64
          - os: linux
            arch: arm
          - os: linux
            arch: arm64
          - os: windows
            arch: amd64
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit
      - name: Install arm build toolchains
        run: |
          sudo apt-get update
          sudo apt-get install gcc-arm-linux-gnueabihf gcc-aarch64-linux-gnu
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup Golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'go.mod'
          check-latest: true
          cache-dependency-path: |
            go.sum
      - name: Build binary for linux
        if: matrix.os == 'linux'
        run: |
          rm -rf ./bin
          ARCH=${{ matrix.arch }} make bin/azure-acr-credential-provider
          mv bin/azure-acr-credential-provider bin/azure-acr-credential-provider-${{ matrix.os }}-${{ matrix.arch }}
      - name: Build binary for windows
        if: matrix.os == 'windows'
        run: |
          rm -rf ./bin
          ARCH=${{ matrix.arch }} make bin/azure-acr-credential-provider.exe
          mv bin/azure-acr-credential-provider.exe bin/azure-acr-credential-provider-${{ matrix.os }}-${{ matrix.arch }}.exe
      - name: Upload artifact for linux
        if: matrix.os == 'linux'
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: acr-credential-provider-${{ matrix.os }}-${{ matrix.arch }}
          path: bin/azure-acr-credential-provider-${{ matrix.os }}-${{ matrix.arch }}
      - name: Upload artifact for windows
        if: matrix.os == 'windows'
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: acr-credential-provider-${{ matrix.os }}-${{ matrix.arch }}
          path: bin/azure-acr-credential-provider-${{ matrix.os }}-${{ matrix.arch }}.exe
          if-no-files-found: error

  publish:
    runs-on: ubuntu-latest
    needs:
      - build-cloud-controller-manager
      - build-cloud-node-manager
      - build-acr-credential-provider
    permissions:
      contents: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit
      - name: Install arm build toolchains
        run: |
          sudo apt-get update
          sudo apt-get install gcc-arm-linux-gnueabihf
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: documentation
          fetch-depth: 0
      - name: Setup Golang
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: '>=1.22'
          check-latest: true
      - name: Generate release note
        env:
          GITHUB_TOKEN: ${{ github.token }}
        run: |
          ./hack/generate-release-note.sh ${GITHUB_REF_NAME} release-notes.md true
      - name: Download artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          path: ./artifacts
      - name: Publish release
        uses: softprops/action-gh-release@da05d552573ad5aba039eaac05058a918a7bf631 # v2.2.2
        with:
          body_path: release-notes.md
          draft: true
          files: |
            ./artifacts/cloud-node-manager-*-*/*
            ./artifacts/cloud-controller-manager-*-*/*
            ./artifacts/acr-credential-provider-*-*/*
      - name: Update site release note
        uses: stefanzweifel/git-auto-commit-action@b863ae1933cb653a53c021fe36dbb774e1fb9403 # v5.2.0
        with:
          # Optional. Commit message for the created commit.
          # Defaults to "Apply automatic changes"
          commit_message: Update release notes for ${{github.ref_name}}

          # Optional. Local and remote branch name where commit is going to be pushed
          #  to. Defaults to the current branch.
          #  You might need to set `create_branch: true` if the branch does not exist.
          branch: doc/release-note-${{github.ref_name}}

          # Optional glob pattern of files which should be added to the commit
          # Defaults to all (.)
          # See the `pathspec`-documentation for git
          # - https://git-scm.com/docs/git-add#Documentation/git-add.txt-ltpathspecgt82308203
          # - https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddefpathspecapathspec
          file_pattern: './content/en/blog/releases/*.md'
          
          # Optional. Create given branch name in local and remote repository.
          create_branch: true
