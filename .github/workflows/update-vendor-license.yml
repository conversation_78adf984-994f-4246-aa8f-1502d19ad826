# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: "Update vendor licenses"

on:
  workflow_dispatch:
  push:
    branches: [ master, 'release-**' ]
    paths:
      - 'go.*'
  schedule:
    - cron: '0 */4 * * *'
permissions:
  contents: read

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'go' ]

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
      with:
        egress-policy: audit

    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
    - name: Set up Go 1.x
      uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: 'go.mod'
        check-latest: true
        cache-dependency-path: |
          go.sum
      id: go

    - name: Autobuild
      run: |
        make update
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
      with:
        branch: doc/update-vendorlicense-${{github.ref_name}}
        delete-branch: true
        labels: |
            lgtm
            approved
        commit-message: Update vendor license for ${{github.ref_name}}
        add-paths: |
            LICENSES
        body: |
            Auto-generated by GitHub Action [Update vendor licenses](https://github.com/${{github.repository}}/actions/runs/${{github.run_id}})

            #### What type of PR is this?
            /kind testing

            #### What this PR does / why we need it:
            go.sum is out of sync

            #### Does this PR introduce a user-facing change?
            ```release-note
            None
            ```
