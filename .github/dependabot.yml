version: 2
updates:
  - package-ecosystem: "gomod"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/tests"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "20:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/health-probe-proxy"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/kubetest2-aks"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/pkg/azclient"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/pkg/azclient/cache"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: gomod
    directory: /pkg/azclient/client-gen
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: gomod
    directory: /pkg/azclient/configloader
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: gomod
    directory: /pkg/azclient/trace
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/health-probe-proxy"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"

  - package-ecosystem: "gomod"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.33"
    target-branch: "release-1.33"
    ignore:
      - dependency-name: "k8s.io/*"
        versions: [">=0.34.0"]
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.33"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
      - "release-1.33"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.33"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.33"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.32"
    target-branch: "release-1.32"
    ignore:
      - dependency-name: "k8s.io/*"
        versions: [">=0.33.0"]
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.32"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
      - "release-1.32"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.32"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.32"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.30"
    target-branch: "release-1.30"
    ignore:
      - dependency-name: "k8s.io/*"
        versions: [">=0.31.0"]
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.30"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
      - "release-1.30"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.30"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.30"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.31"
    target-branch: "release-1.31"
    ignore:
      - dependency-name: "k8s.io/*"
        versions: [">=0.32.0"]
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.31"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
      - "release-1.31"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.31"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.31"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "gomod"
    directory: "/"
    open-pull-requests-limit: 1
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.29"
    target-branch: "release-1.29"
    ignore:
      - dependency-name: "k8s.io/*"
        versions: [">=0.30.0"]
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.29"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/testing"
      - "lgtm"
      - "approved"
      - "release-1.29"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "release-1.29"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
      - "release-1.29"
    groups:
      all:
        applies-to: version-updates
        patterns:
        - "*"
        update-types:
        - "patch"
        - "minor"
        - "major"
  - package-ecosystem: gomod
    directory: /
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "documentation"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"

  - package-ecosystem: npm
    directory: /
    schedule:
      interval: daily
      time: "01:00"
      timezone: "Asia/Shanghai"
    target-branch: "documentation"
    labels:
      - "area/dependency"
      - "release-note-none"
      - "ok-to-test"
      - "kind/cleanup"
      - "lgtm"
      - "approved"
