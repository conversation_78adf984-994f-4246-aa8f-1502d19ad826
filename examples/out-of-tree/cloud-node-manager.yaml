apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    k8s-app: cloud-node-manager
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
  name: cloud-node-manager
  namespace: kube-system
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cloud-node-manager
  labels:
    k8s-app: cloud-node-manager
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
rules:
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["watch","list","get","update", "patch"]
- apiGroups: [""]
  resources: ["nodes/status"]
  verbs: ["patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cloud-node-manager
  labels:
    k8s-app: cloud-node-manager
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cloud-node-manager
subjects:
- kind: ServiceAccount
  name: cloud-node-manager
  namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cloud-node-manager
  namespace: kube-system
  labels:
    component: cloud-node-manager
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
spec:
  selector:
    matchLabels:
      k8s-app: cloud-node-manager
  template:
    metadata:
      labels:
        k8s-app: cloud-node-manager
      annotations:
        cluster-autoscaler.kubernetes.io/daemonset-pod: "true"
    spec:
      priorityClassName: system-node-critical
      serviceAccountName: cloud-node-manager
      hostNetwork: true   # required to fetch correct hostname
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - key: node-role.kubernetes.io/master
        operator: Equal
        value: "true"
        effect: NoSchedule
      - operator: "Exists"
        effect: NoExecute
      - operator: "Exists"
        effect: NoSchedule
      containers:
      - name: cloud-node-manager
        image: mcr.microsoft.com/oss/kubernetes/azure-cloud-node-manager:v1.33.0
        imagePullPolicy: IfNotPresent
        command:
        - cloud-node-manager
        - --node-name=$(NODE_NAME)
        - --wait-routes=true   # only set to true when --configure-cloud-routes=true in cloud-controller-manager.
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        resources:
          requests:
            cpu: 50m
            memory: 50Mi
          limits:
            cpu: 2000m
            memory: 512Mi

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cloud-node-manager-windows
  namespace: kube-system
  labels:
    component: cloud-node-manager
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
spec:
  selector:
    matchLabels:
      k8s-app: cloud-node-manager-windows
  template:
    metadata:
      labels:
        k8s-app: cloud-node-manager-windows
      annotations:
        cluster-autoscaler.kubernetes.io/daemonset-pod: "true"
    spec:
      priorityClassName: system-node-critical
      serviceAccountName: cloud-node-manager
      nodeSelector:
        kubernetes.io/os: windows
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - key: node-role.kubernetes.io/master
        operator: Equal
        value: "true"
        effect: NoSchedule
      - operator: "Exists"
        effect: NoExecute
      - operator: "Exists"
        effect: NoSchedule
      containers:
      - name: cloud-node-manager
        image: mcr.microsoft.com/oss/kubernetes/azure-cloud-node-manager:v1.33.0
        imagePullPolicy: IfNotPresent
        command:
        - /cloud-node-manager.exe
        - --node-name=$(NODE_NAME)
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        resources:
          requests:
            cpu: 50m
            memory: 50Mi
          limits:
            cpu: 2000m
            memory: 512Mi
