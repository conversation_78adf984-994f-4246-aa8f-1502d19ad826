{"kubernetesConfigurations": {"kube-apiserver": {"image": "{CUSTOM_KUBE_APISERVER_IMAGE}", "config": {"--kubelet-timeout": "10s", "--log-flush-frequency": "10s", "--profiling": "false", "--v": "5"}}, "kube-controller-manager": {"image": "{CUSTOM_KCM_IMAGE}", "config": {"--pod-eviction-timeout": "4m", "--profiling": "true"}}, "kube-proxy": {"image": "{CUSTOM_KUBE_PROXY_IMAGE}", "config": {"--config-sync-period": "10m", "--conntrack-tcp-timeout-established": "12h", "--v": "3"}}, "kube-scheduler": {"image": "{CUSTOM_KUBE_SCHEDULER_IMAGE}", "config": {"--http2-max-streams-per-connection": "200", "--profiling": "false"}}, "kubelet": {"config": {"--runtime-request-timeout": "3m", "--streaming-connection-idle-timeout": "5h", "--v": "3"}, "downloadURL": "{CUSTOM_KUBELET_URL}"}}}