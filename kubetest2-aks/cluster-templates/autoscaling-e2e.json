{"id": "{AKS_CLUSTER_ID}", "name": "{CLUSTER_NAME}", "location": "{AZURE_LOCATION}", "type": "Microsoft.ContainerService/ManagedClusters", "properties": {"kubernetesVersion": "{KUBERNETES_VERSION}", "dnsPrefix": "aks", "agentPoolProfiles": [{"name": "agentpool1", "count": 2, "mode": "System", "vmSize": "Standard_DS2_v2", "osType": "Linux", "availabilityProfile": "VirtualMachineScaleSets", "enableAutoScaling": true, "maxCount": 399, "minCount": 1}], "servicePrincipalProfile": {"clientId": "{AZURE_CLIENT_ID}", "secret": "{AZURE_CLIENT_SECRET}"}, "encodedCustomConfiguration": "{CUSTOM_CONFIG}"}}