// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package interfaceclient

import (
	"context"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armnetwork "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v6"
	. "github.com/onsi/gomega"
)

var subnet *armnetwork.Subnet
var vnetClient *armnetwork.VirtualNetworksClient

func init() {
	additionalTestCases = func() {
	}

	beforeAllFunc = func(ctx context.Context) {
		subscriptionID = recorder.SubscriptionID()
		Expect(err).NotTo(HaveOccurred())
		cred := recorder.TokenCredential()
		networkClientOption := clientOption
		networkClientOption.Telemetry.ApplicationID = "ccm-network-client"
		vnetClient, err = armnetwork.NewVirtualNetworksClient(subscriptionID, cred, &arm.ClientOptions{
			ClientOptions: networkClientOption,
		})

		vnet := armnetwork.VirtualNetwork{
			Location: to.Ptr(location),
			Properties: &armnetwork.VirtualNetworkPropertiesFormat{
				AddressSpace: &armnetwork.AddressSpace{
					AddressPrefixes: []*string{
						to.Ptr("10.0.0.0/16"),
					},
				},
				Subnets: []*armnetwork.Subnet{
					{
						Name: to.Ptr("testSubnet"),
						Properties: &armnetwork.SubnetPropertiesFormat{
							AddressPrefix: to.Ptr("10.0.0.0/24"),
						},
					},
				},
			},
		}
		poller, err := vnetClient.BeginCreateOrUpdate(ctx, resourceGroupName, "testVnet", vnet, nil)
		Expect(err).ShouldNot(HaveOccurred())
		resp, err := poller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
		vnet = resp.VirtualNetwork
		Expect(err).ShouldNot(HaveOccurred())
		Expect(vnet).ShouldNot(BeNil())
		Expect(*vnet.Name).To(Equal("testVnet"))
		subnet = vnet.Properties.Subnets[0]
		newResource = &armnetwork.Interface{
			Location: to.Ptr(location),
			Properties: &armnetwork.InterfacePropertiesFormat{
				IPConfigurations: []*armnetwork.InterfaceIPConfiguration{
					{
						Name: to.Ptr("ipConfig1"),
						Properties: &armnetwork.InterfaceIPConfigurationPropertiesFormat{
							PrivateIPAllocationMethod: to.Ptr(armnetwork.IPAllocationMethodDynamic),
							Subnet:                    subnet,
						},
					},
				},
			},
		}
	}
	afterAllFunc = func(ctx context.Context) {
		poller, err := vnetClient.BeginDelete(ctx, resourceGroupName, "testVnet", nil)
		Expect(err).ShouldNot(HaveOccurred())
		_, err = poller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
		Expect(err).ShouldNot(HaveOccurred())
	}
}
