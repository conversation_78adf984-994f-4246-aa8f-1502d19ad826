// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package interfaceclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/tracing"
	armnetwork "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v6"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/metrics"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

const AzureStackCloudAPIVersion = "2018-11-01"

type Client struct {
	*armnetwork.InterfacesClient
	subscriptionID string
	tracer         tracing.Tracer
}

func New(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (Interface, error) {
	if options == nil {
		options = utils.GetDefaultOption()
	}
	tr := options.TracingProvider.NewTracer(utils.ModuleName, utils.ModuleVersion)

	client, err := armnetwork.NewInterfacesClient(subscriptionID, credential, options)
	if err != nil {
		return nil, err
	}
	return &Client{
		InterfacesClient: client,
		subscriptionID:   subscriptionID,
		tracer:           tr,
	}, nil
}

const GetOperationName = "InterfacesClient.Get"

// Get gets the Interface
func (client *Client) Get(ctx context.Context, resourceGroupName string, interfaceName string, expand *string) (result *armnetwork.Interface, err error) {
	var ops *armnetwork.InterfacesClientGetOptions
	if expand != nil {
		ops = &armnetwork.InterfacesClientGetOptions{Expand: expand}
	}
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Interface", "get")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, GetOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := client.InterfacesClient.Get(ctx, resourceGroupName, interfaceName, ops)
	if err != nil {
		return nil, err
	}
	//handle statuscode
	return &resp.Interface, nil
}

const CreateOrUpdateOperationName = "InterfacesClient.Create"

// CreateOrUpdate creates or updates a Interface.
func (client *Client) CreateOrUpdate(ctx context.Context, resourceGroupName string, interfaceName string, resource armnetwork.Interface) (result *armnetwork.Interface, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Interface", "create_or_update")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, CreateOrUpdateOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := utils.NewPollerWrapper(client.InterfacesClient.BeginCreateOrUpdate(ctx, resourceGroupName, interfaceName, resource, nil)).WaitforPollerResp(ctx)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &resp.Interface, nil
	}
	return nil, nil
}

const DeleteOperationName = "InterfacesClient.Delete"

// Delete deletes a Interface by name.
func (client *Client) Delete(ctx context.Context, resourceGroupName string, interfaceName string) (err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Interface", "delete")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, DeleteOperationName, client.tracer, nil)
	defer endSpan(err)
	_, err = utils.NewPollerWrapper(client.BeginDelete(ctx, resourceGroupName, interfaceName, nil)).WaitforPollerResp(ctx)
	return err
}

const ListOperationName = "InterfacesClient.List"

// List gets a list of Interface in the resource group.
func (client *Client) List(ctx context.Context, resourceGroupName string) (result []*armnetwork.Interface, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Interface", "list")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, ListOperationName, client.tracer, nil)
	defer endSpan(err)
	pager := client.InterfacesClient.NewListPager(resourceGroupName, nil)
	for pager.More() {
		nextResult, err := pager.NextPage(ctx)
		if err != nil {
			return nil, err
		}
		result = append(result, nextResult.Value...)
	}
	return result, nil
}
