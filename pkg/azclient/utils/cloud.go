/*
Copyright 2023 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package utils

import (
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/cloud"
)

var EnvironmentMapping = map[string]*cloud.Configuration{
	"AZURECHINACLOUD":        &cloud.AzureChina,
	"AZURECLOUD":             &cloud.AzurePublic,
	"AZUREPUBLICCLOUD":       &cloud.AzurePublic,
	"AZUREUSGOVERNMENT":      &cloud.AzureGovernment,
	"AZUREUSGOVERNMENTCLOUD": &cloud.AzureGovernment, //TODO: deprecate
}

func AzureCloudConfigFromName(cloudName string) *cloud.Configuration {
	cloudName = strings.ToUpper(strings.TrimSpace(cloudName))
	if cloudConfig, ok := EnvironmentMapping[cloudName]; ok {
		return cloudConfig
	}
	return &cloud.AzurePublic
}
