// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package accountclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armstorage "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/storage/armstorage"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var newResource *armstorage.Account

func init() {
	additionalTestCases = func() {
		When("creation requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				resourceName = "akscitaccountsdktest"
				body := &armstorage.AccountCreateParameters{
					Location: to.Ptr(location),
					Kind:     to.Ptr(armstorage.KindStorage),
					Properties: &armstorage.AccountPropertiesCreateParameters{
						AllowBlobPublicAccess:        to.Ptr(false),
						AllowSharedKeyAccess:         to.Ptr(false),
						DefaultToOAuthAuthentication: to.Ptr(false),
						Encryption: &armstorage.Encryption{
							KeySource:                       to.Ptr(armstorage.KeySourceMicrosoftStorage),
							RequireInfrastructureEncryption: to.Ptr(false),
							Services: &armstorage.EncryptionServices{
								Blob: &armstorage.EncryptionService{
									Enabled: to.Ptr(true),
									KeyType: to.Ptr(armstorage.KeyTypeAccount),
								},
								File: &armstorage.EncryptionService{
									Enabled: to.Ptr(true),
									KeyType: to.Ptr(armstorage.KeyTypeAccount),
								},
							},
						},
						IsHnsEnabled:  to.Ptr(false),
						IsSftpEnabled: to.Ptr(false),
						KeyPolicy: &armstorage.KeyPolicy{
							KeyExpirationPeriodInDays: to.Ptr[int32](20),
						},
					},
					SKU: &armstorage.SKU{
						Name: to.Ptr(armstorage.SKUNameStandardGRS),
					},
				}
				if location != "chinaeast2" {
					body.Properties.MinimumTLSVersion = to.Ptr(armstorage.MinimumTLSVersionTLS12)
				}
				newResource, err := realClient.Create(ctx, resourceGroupName, resourceName, body)
				Expect(err).NotTo(HaveOccurred())
				Expect(newResource).NotTo(BeNil())
				Expect(*newResource.Name).To(Equal(resourceName))
			})
		})

		When("get requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				newResource, err := realClient.GetProperties(ctx, resourceGroupName, resourceName, nil)
				Expect(err).NotTo(HaveOccurred())
				Expect(newResource).NotTo(BeNil())
			})
		})
		When("invalid get requests are raised", func() {
			It("should return 404 error", func(ctx context.Context) {
				newResource, err := realClient.GetProperties(ctx, resourceGroupName, resourceName+"notfound", nil)
				Expect(err).To(HaveOccurred())
				Expect(newResource).To(BeNil())
			})
		})
		When("update requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				newResource, err := realClient.Update(ctx, resourceGroupName, resourceName, &armstorage.AccountUpdateParameters{
					Properties: &armstorage.AccountPropertiesUpdateParameters{
						AllowBlobPublicAccess: to.Ptr(false),
						AllowSharedKeyAccess:  to.Ptr(false),
					},
				})
				Expect(err).NotTo(HaveOccurred())
				Expect(*newResource.Properties.AllowBlobPublicAccess).To(BeFalse())
			})
			It("should not return error when body is nil", func(ctx context.Context) {
				newResource, err := realClient.Update(ctx, resourceGroupName, resourceName, nil)
				Expect(err).NotTo(HaveOccurred())
				Expect(newResource).NotTo(BeNil())
			})
		})

		When("listkeys requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				keys, err := realClient.ListKeys(ctx, resourceGroupName, resourceName)
				Expect(err).NotTo(HaveOccurred())
				Expect(keys).To(HaveLen(2))
			})
		})
	}

	beforeAllFunc = func(ctx context.Context) {
	}
	afterAllFunc = func(ctx context.Context) {
		err = realClient.Delete(ctx, resourceGroupName, resourceName)
		Expect(err).NotTo(HaveOccurred())
	}
}
