---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourcegroups/aks-cit-Account?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 231
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account","name":"aks-cit-Account","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "231"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 459
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"kind":"Storage","location":"chinaeast2","properties":{"allowBlobPublicAccess":false,"allowSharedKeyAccess":false,"defaultToOAuthAuthentication":false,"encryption":{"keySource":"Microsoft.Storage","requireInfrastructureEncryption":false,"services":{"blob":{"enabled":true,"keyType":"Account"},"file":{"enabled":true,"keyType":"Account"}}},"isHnsEnabled":false,"isSftpEnabled":false,"keyPolicy":{"keyExpirationPeriodInDays":20}},"sku":{"name":"Standard_GRS"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "459"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest?api-version=2023-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Storage/locations/chinaeast2/asyncoperations/********-0000-0000-0000-************?api-version=2023-05-01&c=c&h=h&monitor=true&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Storage/locations/chinaeast2/asyncoperations/********-0000-0000-0000-************?api-version=2023-05-01&c=c&h=h&monitor=true&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1614
        uncompressed: false
        body: '{"sku":{"name":"Standard_GRS","tier":"Standard"},"kind":"Storage","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest","name":"akscitaccountsdktest","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"defaultToOAuthAuthentication":false,"keyPolicy":{"keyExpirationPeriodInDays":20},"keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"privateEndpointConnections":[],"isSftpEnabled":false,"minimumTlsVersion":"TLS1_0","allowBlobPublicAccess":false,"allowSharedKeyAccess":false,"isHnsEnabled":false,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Allow"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"blob":"https://akscitaccountsdktest.blob.core.chinacloudapi.cn/","queue":"https://akscitaccountsdktest.queue.core.chinacloudapi.cn/","table":"https://akscitaccountsdktest.table.core.chinacloudapi.cn/","file":"https://akscitaccountsdktest.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available","secondaryLocation":"chinanorth2","statusOfSecondary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1614"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1614
        uncompressed: false
        body: '{"sku":{"name":"Standard_GRS","tier":"Standard"},"kind":"Storage","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest","name":"akscitaccountsdktest","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"defaultToOAuthAuthentication":false,"keyPolicy":{"keyExpirationPeriodInDays":20},"keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"privateEndpointConnections":[],"isSftpEnabled":false,"minimumTlsVersion":"TLS1_0","allowBlobPublicAccess":false,"allowSharedKeyAccess":false,"isHnsEnabled":false,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Allow"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"blob":"https://akscitaccountsdktest.blob.core.chinacloudapi.cn/","queue":"https://akscitaccountsdktest.queue.core.chinacloudapi.cn/","table":"https://akscitaccountsdktest.table.core.chinacloudapi.cn/","file":"https://akscitaccountsdktest.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available","secondaryLocation":"chinanorth2","statusOfSecondary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1614"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktestnotfound?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 249
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.Storage/storageAccounts/akscitaccountsdktestnotfound'' under resource group ''aks-cit-Account'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "249"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 75
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"properties":{"allowBlobPublicAccess":false,"allowSharedKeyAccess":false}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "75"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest?api-version=2023-05-01
        method: PATCH
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1614
        uncompressed: false
        body: '{"sku":{"name":"Standard_GRS","tier":"Standard"},"kind":"Storage","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest","name":"akscitaccountsdktest","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"defaultToOAuthAuthentication":false,"keyPolicy":{"keyExpirationPeriodInDays":20},"keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"privateEndpointConnections":[],"isSftpEnabled":false,"minimumTlsVersion":"TLS1_0","allowBlobPublicAccess":false,"allowSharedKeyAccess":false,"isHnsEnabled":false,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Allow"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"blob":"https://akscitaccountsdktest.blob.core.chinacloudapi.cn/","queue":"https://akscitaccountsdktest.queue.core.chinacloudapi.cn/","table":"https://akscitaccountsdktest.table.core.chinacloudapi.cn/","file":"https://akscitaccountsdktest.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available","secondaryLocation":"chinanorth2","statusOfSecondary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1614"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 2
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "2"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest?api-version=2023-05-01
        method: PATCH
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1614
        uncompressed: false
        body: '{"sku":{"name":"Standard_GRS","tier":"Standard"},"kind":"Storage","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest","name":"akscitaccountsdktest","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"defaultToOAuthAuthentication":false,"keyPolicy":{"keyExpirationPeriodInDays":20},"keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"privateEndpointConnections":[],"isSftpEnabled":false,"minimumTlsVersion":"TLS1_0","allowBlobPublicAccess":false,"allowSharedKeyAccess":false,"isHnsEnabled":false,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Allow"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"blob":"https://akscitaccountsdktest.blob.core.chinacloudapi.cn/","queue":"https://akscitaccountsdktest.queue.core.chinacloudapi.cn/","table":"https://akscitaccountsdktest.table.core.chinacloudapi.cn/","file":"https://akscitaccountsdktest.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available","secondaryLocation":"chinanorth2","statusOfSecondary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1614"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest/listKeys?api-version=2023-05-01
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 380
        uncompressed: false
        body: '{"keys":[{"creationTime":"2001-02-03T04:05:06Z","keyName":"key1","value":"{KEY}","permissions":"FULL"},{"creationTime":"2001-02-03T04:05:06Z","keyName":"key2","value":"{KEY}","permissions":"FULL"}]}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "380"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1626
        uncompressed: false
        body: '{"value":[{"sku":{"name":"Standard_GRS","tier":"Standard"},"kind":"Storage","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest","name":"akscitaccountsdktest","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"defaultToOAuthAuthentication":false,"keyPolicy":{"keyExpirationPeriodInDays":20},"keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"privateEndpointConnections":[],"isSftpEnabled":false,"minimumTlsVersion":"TLS1_0","allowBlobPublicAccess":false,"allowSharedKeyAccess":false,"isHnsEnabled":false,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Allow"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"blob":"https://akscitaccountsdktest.blob.core.chinacloudapi.cn/","queue":"https://akscitaccountsdktest.queue.core.chinacloudapi.cn/","table":"https://akscitaccountsdktest.table.core.chinacloudapi.cn/","file":"https://akscitaccountsdktest.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available","secondaryLocation":"chinanorth2","statusOfSecondary":"available"}}]}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1626"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Accountnotfound/providers/Microsoft.Storage/storageAccounts?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 115
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-Accountnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "115"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-Account-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-Account/providers/Microsoft.Storage/storageAccounts/akscitaccountsdktest?api-version=2023-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourcegroups/aks-cit-Account?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRBQ0NPVU5ULUNISU5BRUFTVDIiLCJqb2JMb2NhdGlvbiI6ImNoaW5hZWFzdDIifQ?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRBQ0NPVU5ULUNISU5BRUFTVDIiLCJqb2JMb2NhdGlvbiI6ImNoaW5hZWFzdDIifQ?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
