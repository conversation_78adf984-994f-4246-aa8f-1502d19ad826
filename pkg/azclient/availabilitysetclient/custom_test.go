// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package availabilitysetclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armcompute "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v6"
	. "github.com/onsi/gomega"
)

var computeClientFactory *armcompute.ClientFactory
var availabilitySetsClient *armcompute.AvailabilitySetsClient

func init() {
	additionalTestCases = func() {
	}

	beforeAllFunc = func(ctx context.Context) {
		computeClientOption := clientOption
		computeClientOption.Telemetry.ApplicationID = "ccm-compute-client"
		computeClientFactory, err = armcompute.NewClientFactory(subscriptionID, recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: computeClientOption,
		})
		Expect(err).NotTo(HaveOccurred())
		availabilitySetsClient = computeClientFactory.NewAvailabilitySetsClient()
		_, err = availabilitySetsClient.CreateOrUpdate(
			ctx,
			resourceGroupName,
			resourceName,
			armcompute.AvailabilitySet{
				Location: to.Ptr(location),
				Properties: &armcompute.AvailabilitySetProperties{
					PlatformFaultDomainCount:  to.Ptr[int32](1),
					PlatformUpdateDomainCount: to.Ptr[int32](1),
				},
				SKU: &armcompute.SKU{
					Name: to.Ptr("Aligned"),
				},
			},
			nil,
		)
		Expect(err).NotTo(HaveOccurred())
	}
	afterAllFunc = func(ctx context.Context) {
	}
}
