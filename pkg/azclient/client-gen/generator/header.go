/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package generator

import (
	"io"

	"sigs.k8s.io/controller-tools/pkg/genall"
)

func DumpHeaderToWriter(ctx *genall.GenerationContext, writer io.Writer, headerFile string, importList map[string]map[string]struct{}, pkgName string) error {
	if headerFile != "" {
		headerFileReader, err := ctx.OpenForRead(headerFile)
		if err != nil {
			return err
		}
		defer headerFileReader.Close()
		if _, err = io.Co<PERSON>(writer, headerFileReader); err != nil {
			return err
		}
	}

	if err := HeaderTemplate.Execute(writer, HeaderTemplateVariable{PackageName: pkgName, ImportList: importList}); err != nil {
		return err
	}

	return nil
}
