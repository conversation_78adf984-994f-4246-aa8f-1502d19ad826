// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package blobcontainerclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armstorage "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/storage/armstorage"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/accountclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

var (
	storageClientFactory *armstorage.ClientFactory
	storageaccountClient *armstorage.AccountsClient
	storageAccount       *armstorage.Account
)

func init() {
	additionalTestCases = func() {
		When("creating a container", func() {
			It("should not return an error", func(ctx context.Context) {
				resourceName = "akscitblobsdktest"
				newResource, err := realClient.CreateContainer(ctx, resourceGroupName, accountName, resourceName, armstorage.BlobContainer{
					ContainerProperties: &armstorage.ContainerProperties{
						PublicAccess: to.Ptr(armstorage.PublicAccessNone),
					},
				})
				Expect(err).NotTo(HaveOccurred())
				Expect(*newResource.Name).To(Equal(resourceName))
			})
		})

		When("list a container ", func() {
			It("should not return an error", func(ctx context.Context) {
				lists, err := realClient.List(ctx, resourceGroupName, accountName)
				Expect(err).NotTo(HaveOccurred())
				Expect(lists).To(HaveLen(1))
				Expect(*lists[0].Name).To(Equal(resourceName))
			})
		})
	}

	beforeAllFunc = func(ctx context.Context) {
		storageClientOption := clientOption
		storageClientOption.Telemetry.ApplicationID = "ccm-storage-client"
		if location == "chinaeast2" {
			storageClientOption.APIVersion = accountclient.MooncakeApiVersion
		}
		storageClientFactory, err = armstorage.NewClientFactory(subscriptionID, recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: storageClientOption,
		})
		Expect(err).NotTo(HaveOccurred())
		storageaccountClient = storageClientFactory.NewAccountsClient()
		accountName = "akscitblobsdktestparent"
		storageAccount, err := utils.NewPollerWrapper(storageaccountClient.BeginCreate(ctx, resourceGroupName, accountName, armstorage.AccountCreateParameters{
			Location: to.Ptr(location),
			Kind:     to.Ptr(armstorage.KindStorageV2),
			Properties: &armstorage.AccountPropertiesCreateParameters{
				DNSEndpointType:              to.Ptr(armstorage.DNSEndpointTypeStandard),
				DefaultToOAuthAuthentication: to.Ptr(false),
				AllowBlobPublicAccess:        to.Ptr(false),
				AllowCrossTenantReplication:  to.Ptr(false),
				IsHnsEnabled:                 to.Ptr(true),
				MinimumTLSVersion:            to.Ptr(armstorage.MinimumTLSVersionTLS12),
				AllowSharedKeyAccess:         to.Ptr(true),
				PublicNetworkAccess:          to.Ptr(armstorage.PublicNetworkAccessDisabled),
				IsLocalUserEnabled:           to.Ptr(true),
				LargeFileSharesState:         to.Ptr(armstorage.LargeFileSharesStateEnabled),
				IsSftpEnabled:                to.Ptr(true),
				EnableNfsV3:                  to.Ptr(true),
				EnableHTTPSTrafficOnly:       to.Ptr(true),
				NetworkRuleSet: &armstorage.NetworkRuleSet{
					Bypass:        to.Ptr(armstorage.BypassAzureServices),
					DefaultAction: to.Ptr(armstorage.DefaultActionDeny),
					IPRules:       []*armstorage.IPRule{},
				},
				Encryption: &armstorage.Encryption{
					RequireInfrastructureEncryption: to.Ptr(false),
					KeySource:                       to.Ptr(armstorage.KeySourceMicrosoftStorage),
					Services: &armstorage.EncryptionServices{
						File: &armstorage.EncryptionService{
							KeyType: to.Ptr(armstorage.KeyTypeAccount),
							Enabled: to.Ptr(true),
						},
						Blob: &armstorage.EncryptionService{
							KeyType: to.Ptr(armstorage.KeyTypeAccount),
							Enabled: to.Ptr(true),
						},
					},
				},
				AccessTier: to.Ptr(armstorage.AccessTierCool),
			},
			SKU: &armstorage.SKU{
				Name: to.Ptr(armstorage.SKUNameStandardLRS),
				Tier: to.Ptr(armstorage.SKUTierStandard),
			},
		}, nil)).WaitforPollerResp(ctx)
		Expect(err).NotTo(HaveOccurred())
		Expect(storageAccount).NotTo(BeNil())
		Expect(*storageAccount.Name).To(Equal(accountName))
	}
	afterAllFunc = func(ctx context.Context) {
		err = realClient.DeleteContainer(ctx, resourceGroupName, accountName, resourceName)
		Expect(err).NotTo(HaveOccurred())

		_, err = storageaccountClient.Delete(ctx, resourceGroupName, accountName, nil)
		Expect(err).NotTo(HaveOccurred())
	}
}
