---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourcegroups/aks-cit-BlobContainer?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 243
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer","name":"aks-cit-BlobContainer","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "243"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 768
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"kind":"StorageV2","location":"chinaeast2","properties":{"accessTier":"Cool","allowBlobPublicAccess":false,"allowCrossTenantReplication":false,"allowSharedKeyAccess":true,"defaultToOAuthAuthentication":false,"dnsEndpointType":"Standard","encryption":{"keySource":"Microsoft.Storage","requireInfrastructureEncryption":false,"services":{"blob":{"enabled":true,"keyType":"Account"},"file":{"enabled":true,"keyType":"Account"}}},"isHnsEnabled":true,"isLocalUserEnabled":true,"isNfsV3Enabled":true,"isSftpEnabled":true,"largeFileSharesState":"Enabled","minimumTlsVersion":"TLS1_2","networkAcls":{"bypass":"AzureServices","defaultAction":"Deny","ipRules":[]},"publicNetworkAccess":"Disabled","supportsHttpsTrafficOnly":true},"sku":{"name":"Standard_LRS","tier":"Standard"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "768"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-storage-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent?api-version=2023-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Storage/locations/chinaeast2/asyncoperations/********-0000-0000-0000-************?api-version=2023-05-01&c=c&h=h&monitor=true&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-storage-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Storage/locations/chinaeast2/asyncoperations/********-0000-0000-0000-************?api-version=2023-05-01&c=c&h=h&monitor=true&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1861
        uncompressed: false
        body: '{"sku":{"name":"Standard_LRS","tier":"Standard"},"kind":"StorageV2","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent","name":"akscitblobsdktestparent","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"dnsEndpointType":"Standard","defaultToOAuthAuthentication":false,"publicNetworkAccess":"Disabled","keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"allowCrossTenantReplication":false,"privateEndpointConnections":[],"isNfsV3Enabled":true,"isLocalUserEnabled":true,"isSftpEnabled":true,"minimumTlsVersion":"TLS1_2","allowBlobPublicAccess":false,"allowSharedKeyAccess":true,"largeFileSharesState":"Enabled","isHnsEnabled":true,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Deny"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"accessTier":"Cool","provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"dfs":"https://akscitblobsdktestparent.dfs.core.chinacloudapi.cn/","web":"https://akscitblobsdktestparent.z4.web.core.chinacloudapi.cn/","blob":"https://akscitblobsdktestparent.blob.core.chinacloudapi.cn/","queue":"https://akscitblobsdktestparent.queue.core.chinacloudapi.cn/","table":"https://akscitblobsdktestparent.table.core.chinacloudapi.cn/","file":"https://akscitblobsdktestparent.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1861"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 38
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"properties":{"publicAccess":"None"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "38"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-BlobContainer-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktest?api-version=2023-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 440
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktest","name":"akscitblobsdktest","type":"Microsoft.Storage/storageAccounts/blobServices/containers","properties":{"deleted":false,"remainingRetentionDays":0,"publicAccess":"None","hasImmutabilityPolicy":false,"hasLegalHold":false}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "440"
            Content-Type:
                - application/json
            Etag:
                - '"0x8DD5AE7532733FB"'
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-BlobContainer-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 721
        uncompressed: false
        body: '{"value":[{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktest","name":"akscitblobsdktest","type":"Microsoft.Storage/storageAccounts/blobServices/containers","etag":"\"0x8DD5AE7532733FB\"","properties":{"immutableStorageWithVersioning":{"enabled":false},"deleted":false,"remainingRetentionDays":0,"defaultEncryptionScope":"$account-encryption-key","denyEncryptionScopeOverride":false,"publicAccess":"None","leaseStatus":"Unlocked","leaseState":"Available","lastModifiedTime":"2001-02-03T04:05:06Z","hasImmutabilityPolicy":false,"hasLegalHold":false}}]}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "721"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-BlobContainer-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktest?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 703
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktest","name":"akscitblobsdktest","type":"Microsoft.Storage/storageAccounts/blobServices/containers","etag":"\"0x8DD5AE7532733FB\"","properties":{"deleted":false,"remainingRetentionDays":0,"defaultEncryptionScope":"$account-encryption-key","denyEncryptionScopeOverride":false,"publicAccess":"None","leaseStatus":"Unlocked","leaseState":"Available","lastModifiedTime":"2001-02-03T04:05:06Z","legalHold":{"hasLegalHold":false,"tags":[]},"hasImmutabilityPolicy":false,"hasLegalHold":false}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "703"
            Content-Type:
                - application/json
            Etag:
                - '"0x8DD5AE7532733FB"'
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-BlobContainer-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktestnotfound?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 173
        uncompressed: false
        body: '{"error":{"code":"ContainerNotFound","message":"The specified container does not exist.\nRequestId:********-0000-0000-0000-************\nTime:2001-02-03T04:05:06Z"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "173"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-BlobContainer-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent/blobServices/default/containers/akscitblobsdktest?api-version=2023-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-storage-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-BlobContainer/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestparent?api-version=2023-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/resourcegroups/aks-cit-BlobContainer?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRCTE9CQ09OVEFJTkVSLUNISU5BRUFTVDIiLCJqb2JMb2NhdGlvbiI6ImNoaW5hZWFzdDIifQ?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-************/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRCTE9CQ09OVEFJTkVSLUNISU5BRUFTVDIiLCJqb2JMb2NhdGlvbiI6ImNoaW5hZWFzdDIifQ?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
