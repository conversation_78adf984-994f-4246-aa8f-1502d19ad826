---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 245
        uncompressed: false
        body: '{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster","name":"aks-cit-ManagedCluster","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "245"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 587
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"identity":{"type":"SystemAssigned"},"location":"chinaeast2","properties":{"agentPoolProfiles":[{"count":3,"mode":"System","name":"agentpool1","vmSize":"Standard_D2s_v3"}],"dnsPrefix":"aks-cit-ManagedClustertestResourcednsPrefix","enableRBAC":true,"linuxProfile":{"adminUsername":"azureuser","ssh":{"publicKeys":[{"keyData":"ssh-rsa {KEY}\n"}]}}},"sku":{"name":"Base","tier":"Standard"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "587"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource?api-version=2024-10-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 3370
        uncompressed: false
        body: |-
            {
             "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource",
             "location": "chinaeast2",
             "name": "testResource",
             "type": "Microsoft.ContainerService/ManagedClusters",
             "properties": {
              "provisioningState": "Creating",
              "powerState": {
               "code": "Running"
              },
              "kubernetesVersion": "1.30",
              "currentKubernetesVersion": "1.30.9",
              "dnsPrefix": "aks-cit-ManagedClustertestResourcednsPrefix",
              "fqdn": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "azurePortalFQDN": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.portal.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "agentPoolProfiles": [
               {
                "name": "agentpool1",
                "count": 3,
                "vmSize": "Standard_D2s_v3",
                "osDiskSizeGB": 128,
                "osDiskType": "Managed",
                "kubeletDiskType": "OS",
                "maxPods": 250,
                "type": "VirtualMachineScaleSets",
                "scaleDownMode": "Delete",
                "provisioningState": "Creating",
                "powerState": {
                 "code": "Running"
                },
                "orchestratorVersion": "1.30",
                "currentOrchestratorVersion": "1.30.9",
                "enableNodePublicIP": false,
                "nodeLabels": {},
                "mode": "System",
                "osType": "Linux",
                "osSKU": "Ubuntu",
                "nodeImageVersion": "AKSUbuntu-2204gen2containerd-202502.09.0",
                "upgradeSettings": {
                 "maxSurge": "10%"
                },
                "enableFIPS": false,
                "securityProfile": {
                 "enableVTPM": false,
                 "enableSecureBoot": false
                }
               }
              ],
              "linuxProfile": {
               "adminUsername": "azureuser",
               "ssh": {
                "publicKeys": [
                 {
                  "keyData": "ssh-rsa {KEY}\n"
                 }
                ]
               }
              },
              "servicePrincipalProfile": {
               "clientId": "msi"
              },
              "nodeResourceGroup": "MC_aks-cit-ManagedCluster_testResource_chinaeast2",
              "enableRBAC": true,
              "supportPlan": "KubernetesOfficial",
              "networkProfile": {
               "networkPlugin": "azure",
               "networkPluginMode": "overlay",
               "networkPolicy": "none",
               "networkDataplane": "azure",
               "loadBalancerSku": "standard",
               "loadBalancerProfile": {
                "managedOutboundIPs": {
                 "count": 1
                },
                "backendPoolType": "nodeIPConfiguration"
               },
               "podCidr": "**********/16",
               "serviceCidr": "10.0.0.0/16",
               "dnsServiceIP": "*********",
               "outboundType": "loadBalancer",
               "podCidrs": [
                "**********/16"
               ],
               "serviceCidrs": [
                "10.0.0.0/16"
               ],
               "ipFamilies": [
                "IPv4"
               ]
              },
              "maxAgentPools": 100,
              "autoUpgradeProfile": {
               "nodeOSUpgradeChannel": "NodeImage"
              },
              "securityProfile": {},
              "storageProfile": {
               "diskCSIDriver": {
                "enabled": true
               },
               "fileCSIDriver": {
                "enabled": true
               },
               "snapshotController": {
                "enabled": true
               }
              },
              "oidcIssuerProfile": {
               "enabled": false
              },
              "workloadAutoScalerProfile": {},
              "resourceUID": "67c6923ff0aa4f000103f186",
              "metricsProfile": {
               "costAnalysis": {
                "enabled": false
               }
              }
             },
             "identity": {
              "type": "SystemAssigned",
              "principalId": "00000000-0000-0000-0000-000000000000",
              "tenantId": "tenantid"
             },
             "sku": {
              "name": "Base",
              "tier": "Standard"
             }
            }
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "3370"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 165
        uncompressed: false
        body: |-
            {
             "name": "00000000-0000-0000-0000-000000000000",
             "status": "Succeeded",
             "startTime": "2001-02-03T04:05:06Z",
             "endTime": "2001-02-03T04:05:06Z"
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "165"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource?api-version=2024-10-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 4003
        uncompressed: false
        body: |-
            {
             "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource",
             "location": "chinaeast2",
             "name": "testResource",
             "type": "Microsoft.ContainerService/ManagedClusters",
             "properties": {
              "provisioningState": "Succeeded",
              "powerState": {
               "code": "Running"
              },
              "kubernetesVersion": "1.30",
              "currentKubernetesVersion": "1.30.9",
              "dnsPrefix": "aks-cit-ManagedClustertestResourcednsPrefix",
              "fqdn": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "azurePortalFQDN": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.portal.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "agentPoolProfiles": [
               {
                "name": "agentpool1",
                "count": 3,
                "vmSize": "Standard_D2s_v3",
                "osDiskSizeGB": 128,
                "osDiskType": "Managed",
                "kubeletDiskType": "OS",
                "maxPods": 250,
                "type": "VirtualMachineScaleSets",
                "scaleDownMode": "Delete",
                "provisioningState": "Succeeded",
                "powerState": {
                 "code": "Running"
                },
                "orchestratorVersion": "1.30",
                "currentOrchestratorVersion": "1.30.9",
                "enableNodePublicIP": false,
                "mode": "System",
                "osType": "Linux",
                "osSKU": "Ubuntu",
                "nodeImageVersion": "AKSUbuntu-2204gen2containerd-202502.09.0",
                "upgradeSettings": {
                 "maxSurge": "10%"
                },
                "enableFIPS": false,
                "securityProfile": {
                 "enableVTPM": false,
                 "enableSecureBoot": false
                }
               }
              ],
              "linuxProfile": {
               "adminUsername": "azureuser",
               "ssh": {
                "publicKeys": [
                 {
                  "keyData": "ssh-rsa {KEY}\n"
                 }
                ]
               }
              },
              "servicePrincipalProfile": {
               "clientId": "msi"
              },
              "nodeResourceGroup": "MC_aks-cit-ManagedCluster_testResource_chinaeast2",
              "enableRBAC": true,
              "supportPlan": "KubernetesOfficial",
              "networkProfile": {
               "networkPlugin": "azure",
               "networkPluginMode": "overlay",
               "networkPolicy": "none",
               "networkDataplane": "azure",
               "loadBalancerSku": "standard",
               "loadBalancerProfile": {
                "managedOutboundIPs": {
                 "count": 1
                },
                "effectiveOutboundIPs": [
                 {
                  "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.Network/publicIPAddresses/00000000-0000-0000-0000-000000000000"
                 }
                ],
                "backendPoolType": "nodeIPConfiguration"
               },
               "podCidr": "**********/16",
               "serviceCidr": "10.0.0.0/16",
               "dnsServiceIP": "*********",
               "outboundType": "loadBalancer",
               "podCidrs": [
                "**********/16"
               ],
               "serviceCidrs": [
                "10.0.0.0/16"
               ],
               "ipFamilies": [
                "IPv4"
               ]
              },
              "maxAgentPools": 100,
              "identityProfile": {
               "kubeletidentity": {
                "resourceId": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource-agentpool",
                "clientId": "00000000-0000-0000-0000-000000000000",
                "objectId": "00000000-0000-0000-0000-000000000000"
               }
              },
              "autoUpgradeProfile": {
               "nodeOSUpgradeChannel": "NodeImage"
              },
              "securityProfile": {},
              "storageProfile": {
               "diskCSIDriver": {
                "enabled": true
               },
               "fileCSIDriver": {
                "enabled": true
               },
               "snapshotController": {
                "enabled": true
               }
              },
              "oidcIssuerProfile": {
               "enabled": false
              },
              "workloadAutoScalerProfile": {},
              "resourceUID": "67c6923ff0aa4f000103f186",
              "metricsProfile": {
               "costAnalysis": {
                "enabled": false
               }
              }
             },
             "identity": {
              "type": "SystemAssigned",
              "principalId": "00000000-0000-0000-0000-000000000000",
              "tenantId": "tenantid"
             },
             "sku": {
              "name": "Base",
              "tier": "Standard"
             }
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "4003"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource?api-version=2024-10-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 4003
        uncompressed: false
        body: |-
            {
             "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource",
             "location": "chinaeast2",
             "name": "testResource",
             "type": "Microsoft.ContainerService/ManagedClusters",
             "properties": {
              "provisioningState": "Succeeded",
              "powerState": {
               "code": "Running"
              },
              "kubernetesVersion": "1.30",
              "currentKubernetesVersion": "1.30.9",
              "dnsPrefix": "aks-cit-ManagedClustertestResourcednsPrefix",
              "fqdn": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "azurePortalFQDN": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.portal.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "agentPoolProfiles": [
               {
                "name": "agentpool1",
                "count": 3,
                "vmSize": "Standard_D2s_v3",
                "osDiskSizeGB": 128,
                "osDiskType": "Managed",
                "kubeletDiskType": "OS",
                "maxPods": 250,
                "type": "VirtualMachineScaleSets",
                "scaleDownMode": "Delete",
                "provisioningState": "Succeeded",
                "powerState": {
                 "code": "Running"
                },
                "orchestratorVersion": "1.30",
                "currentOrchestratorVersion": "1.30.9",
                "enableNodePublicIP": false,
                "mode": "System",
                "osType": "Linux",
                "osSKU": "Ubuntu",
                "nodeImageVersion": "AKSUbuntu-2204gen2containerd-202502.09.0",
                "upgradeSettings": {
                 "maxSurge": "10%"
                },
                "enableFIPS": false,
                "securityProfile": {
                 "enableVTPM": false,
                 "enableSecureBoot": false
                }
               }
              ],
              "linuxProfile": {
               "adminUsername": "azureuser",
               "ssh": {
                "publicKeys": [
                 {
                  "keyData": "ssh-rsa {KEY}\n"
                 }
                ]
               }
              },
              "servicePrincipalProfile": {
               "clientId": "msi"
              },
              "nodeResourceGroup": "MC_aks-cit-ManagedCluster_testResource_chinaeast2",
              "enableRBAC": true,
              "supportPlan": "KubernetesOfficial",
              "networkProfile": {
               "networkPlugin": "azure",
               "networkPluginMode": "overlay",
               "networkPolicy": "none",
               "networkDataplane": "azure",
               "loadBalancerSku": "standard",
               "loadBalancerProfile": {
                "managedOutboundIPs": {
                 "count": 1
                },
                "effectiveOutboundIPs": [
                 {
                  "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.Network/publicIPAddresses/00000000-0000-0000-0000-000000000000"
                 }
                ],
                "backendPoolType": "nodeIPConfiguration"
               },
               "podCidr": "**********/16",
               "serviceCidr": "10.0.0.0/16",
               "dnsServiceIP": "*********",
               "outboundType": "loadBalancer",
               "podCidrs": [
                "**********/16"
               ],
               "serviceCidrs": [
                "10.0.0.0/16"
               ],
               "ipFamilies": [
                "IPv4"
               ]
              },
              "maxAgentPools": 100,
              "identityProfile": {
               "kubeletidentity": {
                "resourceId": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource-agentpool",
                "clientId": "00000000-0000-0000-0000-000000000000",
                "objectId": "00000000-0000-0000-0000-000000000000"
               }
              },
              "autoUpgradeProfile": {
               "nodeOSUpgradeChannel": "NodeImage"
              },
              "securityProfile": {},
              "storageProfile": {
               "diskCSIDriver": {
                "enabled": true
               },
               "fileCSIDriver": {
                "enabled": true
               },
               "snapshotController": {
                "enabled": true
               }
              },
              "oidcIssuerProfile": {
               "enabled": false
              },
              "workloadAutoScalerProfile": {},
              "resourceUID": "67c6923ff0aa4f000103f186",
              "metricsProfile": {
               "costAnalysis": {
                "enabled": false
               }
              }
             },
             "identity": {
              "type": "SystemAssigned",
              "principalId": "00000000-0000-0000-0000-000000000000",
              "tenantId": "tenantid"
             },
             "sku": {
              "name": "Base",
              "tier": "Standard"
             }
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "4003"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResourcenotfound?api-version=2024-10-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 257
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.ContainerService/managedClusters/testResourcenotfound'' under resource group ''aks-cit-ManagedCluster'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "257"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 587
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"identity":{"type":"SystemAssigned"},"location":"chinaeast2","properties":{"agentPoolProfiles":[{"count":3,"mode":"System","name":"agentpool1","vmSize":"Standard_D2s_v3"}],"dnsPrefix":"aks-cit-ManagedClustertestResourcednsPrefix","enableRBAC":true,"linuxProfile":{"adminUsername":"azureuser","ssh":{"publicKeys":[{"keyData":"ssh-rsa {KEY}\n"}]}}},"sku":{"name":"Base","tier":"Standard"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "587"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource?api-version=2024-10-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 4055
        uncompressed: false
        body: |-
            {
             "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource",
             "location": "chinaeast2",
             "name": "testResource",
             "type": "Microsoft.ContainerService/ManagedClusters",
             "properties": {
              "provisioningState": "Updating",
              "powerState": {
               "code": "Running"
              },
              "kubernetesVersion": "1.30",
              "currentKubernetesVersion": "1.30.9",
              "dnsPrefix": "aks-cit-ManagedClustertestResourcednsPrefix",
              "fqdn": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "azurePortalFQDN": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.portal.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "agentPoolProfiles": [
               {
                "name": "agentpool1",
                "count": 3,
                "vmSize": "Standard_D2s_v3",
                "osDiskSizeGB": 128,
                "osDiskType": "Managed",
                "kubeletDiskType": "OS",
                "maxPods": 250,
                "type": "VirtualMachineScaleSets",
                "enableAutoScaling": false,
                "scaleDownMode": "Delete",
                "provisioningState": "Updating",
                "powerState": {
                 "code": "Running"
                },
                "orchestratorVersion": "1.30",
                "currentOrchestratorVersion": "1.30.9",
                "enableNodePublicIP": false,
                "nodeLabels": {},
                "mode": "System",
                "osType": "Linux",
                "osSKU": "Ubuntu",
                "nodeImageVersion": "AKSUbuntu-2204gen2containerd-202502.09.0",
                "upgradeSettings": {
                 "maxSurge": "10%"
                },
                "enableFIPS": false,
                "securityProfile": {
                 "enableVTPM": false,
                 "enableSecureBoot": false
                }
               }
              ],
              "linuxProfile": {
               "adminUsername": "azureuser",
               "ssh": {
                "publicKeys": [
                 {
                  "keyData": "ssh-rsa {KEY}\n"
                 }
                ]
               }
              },
              "servicePrincipalProfile": {
               "clientId": "msi"
              },
              "nodeResourceGroup": "MC_aks-cit-ManagedCluster_testResource_chinaeast2",
              "enableRBAC": true,
              "supportPlan": "KubernetesOfficial",
              "networkProfile": {
               "networkPlugin": "azure",
               "networkPluginMode": "overlay",
               "networkPolicy": "none",
               "networkDataplane": "azure",
               "loadBalancerSku": "standard",
               "loadBalancerProfile": {
                "managedOutboundIPs": {
                 "count": 1
                },
                "effectiveOutboundIPs": [
                 {
                  "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.Network/publicIPAddresses/00000000-0000-0000-0000-000000000000"
                 }
                ],
                "backendPoolType": "nodeIPConfiguration"
               },
               "podCidr": "**********/16",
               "serviceCidr": "10.0.0.0/16",
               "dnsServiceIP": "*********",
               "outboundType": "loadBalancer",
               "podCidrs": [
                "**********/16"
               ],
               "serviceCidrs": [
                "10.0.0.0/16"
               ],
               "ipFamilies": [
                "IPv4"
               ]
              },
              "maxAgentPools": 100,
              "identityProfile": {
               "kubeletidentity": {
                "resourceId": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource-agentpool",
                "clientId": "00000000-0000-0000-0000-000000000000",
                "objectId": "00000000-0000-0000-0000-000000000000"
               }
              },
              "autoUpgradeProfile": {
               "nodeOSUpgradeChannel": "NodeImage"
              },
              "securityProfile": {},
              "storageProfile": {
               "diskCSIDriver": {
                "enabled": true
               },
               "fileCSIDriver": {
                "enabled": true
               },
               "snapshotController": {
                "enabled": true
               }
              },
              "oidcIssuerProfile": {
               "enabled": false
              },
              "workloadAutoScalerProfile": {},
              "resourceUID": "67c6923ff0aa4f000103f186",
              "metricsProfile": {
               "costAnalysis": {
                "enabled": false
               }
              }
             },
             "identity": {
              "type": "SystemAssigned",
              "principalId": "00000000-0000-0000-0000-000000000000",
              "tenantId": "tenantid"
             },
             "sku": {
              "name": "Base",
              "tier": "Standard"
             }
            }
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "4055"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 164
        uncompressed: false
        body: |-
            {
             "name": "00000000-0000-0000-0000-000000000000",
             "status": "Succeeded",
             "startTime": "2001-02-03T04:05:06Z",
             "endTime": "2001-02-03T04:05:06Z"
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "164"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource?api-version=2024-10-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 4035
        uncompressed: false
        body: |-
            {
             "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource",
             "location": "chinaeast2",
             "name": "testResource",
             "type": "Microsoft.ContainerService/ManagedClusters",
             "properties": {
              "provisioningState": "Succeeded",
              "powerState": {
               "code": "Running"
              },
              "kubernetesVersion": "1.30",
              "currentKubernetesVersion": "1.30.9",
              "dnsPrefix": "aks-cit-ManagedClustertestResourcednsPrefix",
              "fqdn": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "azurePortalFQDN": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.portal.hcp.chinaeast2.cx.prod.service.azk8s.cn",
              "agentPoolProfiles": [
               {
                "name": "agentpool1",
                "count": 3,
                "vmSize": "Standard_D2s_v3",
                "osDiskSizeGB": 128,
                "osDiskType": "Managed",
                "kubeletDiskType": "OS",
                "maxPods": 250,
                "type": "VirtualMachineScaleSets",
                "enableAutoScaling": false,
                "scaleDownMode": "Delete",
                "provisioningState": "Succeeded",
                "powerState": {
                 "code": "Running"
                },
                "orchestratorVersion": "1.30",
                "currentOrchestratorVersion": "1.30.9",
                "enableNodePublicIP": false,
                "mode": "System",
                "osType": "Linux",
                "osSKU": "Ubuntu",
                "nodeImageVersion": "AKSUbuntu-2204gen2containerd-202502.09.0",
                "upgradeSettings": {
                 "maxSurge": "10%"
                },
                "enableFIPS": false,
                "securityProfile": {
                 "enableVTPM": false,
                 "enableSecureBoot": false
                }
               }
              ],
              "linuxProfile": {
               "adminUsername": "azureuser",
               "ssh": {
                "publicKeys": [
                 {
                  "keyData": "ssh-rsa {KEY}\n"
                 }
                ]
               }
              },
              "servicePrincipalProfile": {
               "clientId": "msi"
              },
              "nodeResourceGroup": "MC_aks-cit-ManagedCluster_testResource_chinaeast2",
              "enableRBAC": true,
              "supportPlan": "KubernetesOfficial",
              "networkProfile": {
               "networkPlugin": "azure",
               "networkPluginMode": "overlay",
               "networkPolicy": "none",
               "networkDataplane": "azure",
               "loadBalancerSku": "standard",
               "loadBalancerProfile": {
                "managedOutboundIPs": {
                 "count": 1
                },
                "effectiveOutboundIPs": [
                 {
                  "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.Network/publicIPAddresses/00000000-0000-0000-0000-000000000000"
                 }
                ],
                "backendPoolType": "nodeIPConfiguration"
               },
               "podCidr": "**********/16",
               "serviceCidr": "10.0.0.0/16",
               "dnsServiceIP": "*********",
               "outboundType": "loadBalancer",
               "podCidrs": [
                "**********/16"
               ],
               "serviceCidrs": [
                "10.0.0.0/16"
               ],
               "ipFamilies": [
                "IPv4"
               ]
              },
              "maxAgentPools": 100,
              "identityProfile": {
               "kubeletidentity": {
                "resourceId": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource-agentpool",
                "clientId": "00000000-0000-0000-0000-000000000000",
                "objectId": "00000000-0000-0000-0000-000000000000"
               }
              },
              "autoUpgradeProfile": {
               "nodeOSUpgradeChannel": "NodeImage"
              },
              "securityProfile": {},
              "storageProfile": {
               "diskCSIDriver": {
                "enabled": true
               },
               "fileCSIDriver": {
                "enabled": true
               },
               "snapshotController": {
                "enabled": true
               }
              },
              "oidcIssuerProfile": {
               "enabled": false
              },
              "workloadAutoScalerProfile": {},
              "resourceUID": "67c6923ff0aa4f000103f186",
              "metricsProfile": {
               "costAnalysis": {
                "enabled": false
               }
              }
             },
             "identity": {
              "type": "SystemAssigned",
              "principalId": "00000000-0000-0000-0000-000000000000",
              "tenantId": "tenantid"
             },
             "sku": {
              "name": "Base",
              "tier": "Standard"
             }
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "4035"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters?api-version=2024-10-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 4598
        uncompressed: false
        body: |-
            {
             "value": [
              {
               "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource",
               "location": "chinaeast2",
               "name": "testResource",
               "type": "Microsoft.ContainerService/ManagedClusters",
               "properties": {
                "provisioningState": "Succeeded",
                "powerState": {
                 "code": "Running"
                },
                "kubernetesVersion": "1.30",
                "currentKubernetesVersion": "1.30.9",
                "dnsPrefix": "aks-cit-ManagedClustertestResourcednsPrefix",
                "fqdn": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.hcp.chinaeast2.cx.prod.service.azk8s.cn",
                "azurePortalFQDN": "aks-cit-managedclustertestresourcednsprefix-5h6j8t00.portal.hcp.chinaeast2.cx.prod.service.azk8s.cn",
                "agentPoolProfiles": [
                 {
                  "name": "agentpool1",
                  "count": 3,
                  "vmSize": "Standard_D2s_v3",
                  "osDiskSizeGB": 128,
                  "osDiskType": "Managed",
                  "kubeletDiskType": "OS",
                  "maxPods": 250,
                  "type": "VirtualMachineScaleSets",
                  "enableAutoScaling": false,
                  "scaleDownMode": "Delete",
                  "provisioningState": "Succeeded",
                  "powerState": {
                   "code": "Running"
                  },
                  "orchestratorVersion": "1.30",
                  "currentOrchestratorVersion": "1.30.9",
                  "enableNodePublicIP": false,
                  "mode": "System",
                  "osType": "Linux",
                  "osSKU": "Ubuntu",
                  "nodeImageVersion": "AKSUbuntu-2204gen2containerd-202502.09.0",
                  "upgradeSettings": {
                   "maxSurge": "10%"
                  },
                  "enableFIPS": false,
                  "securityProfile": {
                   "enableVTPM": false,
                   "enableSecureBoot": false
                  }
                 }
                ],
                "linuxProfile": {
                 "adminUsername": "azureuser",
                 "ssh": {
                  "publicKeys": [
                   {
                    "keyData": "ssh-rsa {KEY}\n"
                   }
                  ]
                 }
                },
                "servicePrincipalProfile": {
                 "clientId": "msi"
                },
                "nodeResourceGroup": "MC_aks-cit-ManagedCluster_testResource_chinaeast2",
                "enableRBAC": true,
                "supportPlan": "KubernetesOfficial",
                "networkProfile": {
                 "networkPlugin": "azure",
                 "networkPluginMode": "overlay",
                 "networkPolicy": "none",
                 "networkDataplane": "azure",
                 "loadBalancerSku": "standard",
                 "loadBalancerProfile": {
                  "managedOutboundIPs": {
                   "count": 1
                  },
                  "effectiveOutboundIPs": [
                   {
                    "id": "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.Network/publicIPAddresses/00000000-0000-0000-0000-000000000000"
                   }
                  ],
                  "backendPoolType": "nodeIPConfiguration"
                 },
                 "podCidr": "**********/16",
                 "serviceCidr": "10.0.0.0/16",
                 "dnsServiceIP": "*********",
                 "outboundType": "loadBalancer",
                 "podCidrs": [
                  "**********/16"
                 ],
                 "serviceCidrs": [
                  "10.0.0.0/16"
                 ],
                 "ipFamilies": [
                  "IPv4"
                 ]
                },
                "maxAgentPools": 100,
                "identityProfile": {
                 "kubeletidentity": {
                  "resourceId": "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/MC_aks-cit-ManagedCluster_testResource_chinaeast2/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource-agentpool",
                  "clientId": "00000000-0000-0000-0000-000000000000",
                  "objectId": "00000000-0000-0000-0000-000000000000"
                 }
                },
                "autoUpgradeProfile": {
                 "nodeOSUpgradeChannel": "NodeImage"
                },
                "securityProfile": {},
                "storageProfile": {
                 "diskCSIDriver": {
                  "enabled": true
                 },
                 "fileCSIDriver": {
                  "enabled": true
                 },
                 "snapshotController": {
                  "enabled": true
                 }
                },
                "oidcIssuerProfile": {
                 "enabled": false
                },
                "workloadAutoScalerProfile": {},
                "resourceUID": "67c6923ff0aa4f000103f186",
                "metricsProfile": {
                 "costAnalysis": {
                  "enabled": false
                 }
                }
               },
               "identity": {
                "type": "SystemAssigned",
                "principalId": "00000000-0000-0000-0000-000000000000",
                "tenantId": "tenantid"
               },
               "sku": {
                "name": "Base",
                "tier": "Standard"
               }
              }
             ],
             "nextLink": "https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters?%24skipToken=1893773\u0026api-version=2024-10-01\u0026skipToken=1893773"
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "4598"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters?%24skipToken=1893773&api-version=2024-10-01&skipToken=1893773
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 16
        uncompressed: false
        body: |-
            {
             "value": []
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "16"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedClusternotfound/providers/Microsoft.ContainerService/managedClusters?api-version=2024-10-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 122
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-ManagedClusternotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "122"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-ManagedCluster/providers/Microsoft.ContainerService/managedClusters/testResource?api-version=2024-10-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operationresults/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 13
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-ManagedCluster-clien azsdk-go-armcontainerservice/v6.4.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.ContainerService/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2016-03-30&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 399
        uncompressed: false
        body: |-
            {
             "name": "00000000-0000-0000-0000-000000000000",
             "status": "Succeeded",
             "startTime": "2001-02-03T04:05:06Z",
             "endTime": "2001-02-03T04:05:06Z",
             "error": {
              "code": "NotLatestOperation",
              "message": "Expected goal state for managed cluster not saved yet. This means saving goal state to database was slow or failed. This type of failed operations can usually be retried."
             }
            }
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "399"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 14
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-ManagedCluster?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRNQU5BR0VEQ0xVU1RFUi1DSElOQUVBU1QyIiwiam9iTG9jYXRpb24iOiJjaGluYWVhc3QyIn0?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 15
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRNQU5BR0VEQ0xVU1RFUi1DSElOQUVBU1QyIiwiam9iTG9jYXRpb24iOiJjaGluYWVhc3QyIn0?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
