// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package privatezoneclient

import (
	"context"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/privatedns/armprivatedns"
	. "github.com/onsi/gomega"
)

var (
	privatednsClientFactory *armprivatedns.ClientFactory
	privatednsClient        *armprivatedns.PrivateZonesClient
)

func init() {
	additionalTestCases = func() {
	}

	beforeAllFunc = func(ctx context.Context) {
		dnsClientOption := clientOption
		dnsClientOption.Telemetry.ApplicationID = "ccm-dns-client"
		privatednsClientFactory, err = armprivatedns.NewClientFactory(subscriptionID, recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: dnsClientOption,
		})
		Expect(err).NotTo(HaveOccurred())
		resourceName = "aks-cit-privatednszone-uttest.privatelink.global.azmk8s.io"
		newResource = &armprivatedns.PrivateZone{
			Location: to.Ptr("global"),
		}
	}
	afterAllFunc = func(ctx context.Context) {
		privatednsClient = privatednsClientFactory.NewPrivateZonesClient()
		dnsPoller, err := privatednsClient.BeginDelete(ctx, resourceGroupName, resourceName, nil)
		Expect(err).NotTo(HaveOccurred())
		_, err = dnsPoller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
	}
}
