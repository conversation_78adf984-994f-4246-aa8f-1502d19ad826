// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package privatezoneclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/tracing"
	armprivatedns "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/privatedns/armprivatedns"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/metrics"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

const AzureStackCloudAPIVersion = "2019-07-01"

type Client struct {
	*armprivatedns.PrivateZonesClient
	subscriptionID string
	tracer         tracing.Tracer
}

func New(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (Interface, error) {
	if options == nil {
		options = utils.GetDefaultOption()
	}
	tr := options.TracingProvider.NewTracer(utils.ModuleName, utils.ModuleVersion)

	client, err := armprivatedns.NewPrivateZonesClient(subscriptionID, credential, options)
	if err != nil {
		return nil, err
	}
	return &Client{
		PrivateZonesClient: client,
		subscriptionID:     subscriptionID,
		tracer:             tr,
	}, nil
}

const GetOperationName = "PrivateZonesClient.Get"

// Get gets the PrivateZone
func (client *Client) Get(ctx context.Context, resourceGroupName string, privatezoneName string) (result *armprivatedns.PrivateZone, err error) {

	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PrivateZone", "get")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, GetOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := client.PrivateZonesClient.Get(ctx, resourceGroupName, privatezoneName, nil)
	if err != nil {
		return nil, err
	}
	//handle statuscode
	return &resp.PrivateZone, nil
}

const CreateOrUpdateOperationName = "PrivateZonesClient.Create"

// CreateOrUpdate creates or updates a PrivateZone.
func (client *Client) CreateOrUpdate(ctx context.Context, resourceGroupName string, privatezoneName string, resource armprivatedns.PrivateZone) (result *armprivatedns.PrivateZone, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PrivateZone", "create_or_update")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, CreateOrUpdateOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := utils.NewPollerWrapper(client.PrivateZonesClient.BeginCreateOrUpdate(ctx, resourceGroupName, privatezoneName, resource, nil)).WaitforPollerResp(ctx)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &resp.PrivateZone, nil
	}
	return nil, nil
}
