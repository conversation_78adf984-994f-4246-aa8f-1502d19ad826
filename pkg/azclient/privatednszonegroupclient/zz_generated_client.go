// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package privatednszonegroupclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/tracing"
	armnetwork "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v6"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/metrics"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

type Client struct {
	*armnetwork.PrivateDNSZoneGroupsClient
	subscriptionID string
	tracer         tracing.Tracer
}

func New(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (Interface, error) {
	if options == nil {
		options = utils.GetDefaultOption()
	}
	tr := options.TracingProvider.NewTracer(utils.ModuleName, utils.ModuleVersion)

	client, err := armnetwork.NewPrivateDNSZoneGroupsClient(subscriptionID, credential, options)
	if err != nil {
		return nil, err
	}
	return &Client{
		PrivateDNSZoneGroupsClient: client,
		subscriptionID:             subscriptionID,
		tracer:                     tr,
	}, nil
}

const GetOperationName = "PrivateDNSZoneGroupsClient.Get"

// Get gets the PrivateDNSZoneGroup
func (client *Client) Get(ctx context.Context, resourceGroupName string, privateendpointName string, privatednszonegroupName string) (result *armnetwork.PrivateDNSZoneGroup, err error) {

	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PrivateDNSZoneGroup", "get")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, GetOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := client.PrivateDNSZoneGroupsClient.Get(ctx, resourceGroupName, privateendpointName, privatednszonegroupName, nil)
	if err != nil {
		return nil, err
	}
	//handle statuscode
	return &resp.PrivateDNSZoneGroup, nil
}

const CreateOrUpdateOperationName = "PrivateDNSZoneGroupsClient.Create"

// CreateOrUpdate creates or updates a PrivateDNSZoneGroup.
func (client *Client) CreateOrUpdate(ctx context.Context, resourceGroupName string, privateendpointName string, privatednszonegroupName string, resource armnetwork.PrivateDNSZoneGroup) (result *armnetwork.PrivateDNSZoneGroup, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PrivateDNSZoneGroup", "create_or_update")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, CreateOrUpdateOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := utils.NewPollerWrapper(client.PrivateDNSZoneGroupsClient.BeginCreateOrUpdate(ctx, resourceGroupName, privateendpointName, privatednszonegroupName, resource, nil)).WaitforPollerResp(ctx)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &resp.PrivateDNSZoneGroup, nil
	}
	return nil, nil
}

const DeleteOperationName = "PrivateDNSZoneGroupsClient.Delete"

// Delete deletes a PrivateDNSZoneGroup by name.
func (client *Client) Delete(ctx context.Context, resourceGroupName string, privateendpointName string, privatednszonegroupName string) (err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PrivateDNSZoneGroup", "delete")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, DeleteOperationName, client.tracer, nil)
	defer endSpan(err)
	_, err = utils.NewPollerWrapper(client.BeginDelete(ctx, resourceGroupName, privateendpointName, privatednszonegroupName, nil)).WaitforPollerResp(ctx)
	return err
}
