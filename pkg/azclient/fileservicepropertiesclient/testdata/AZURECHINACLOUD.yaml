---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourcegroups/aks-cit-FileServiceProperties?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 259
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties","name":"aks-cit-FileServiceProperties","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "259"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 768
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"kind":"StorageV2","location":"chinaeast2","properties":{"accessTier":"Cool","allowBlobPublicAccess":false,"allowCrossTenantReplication":false,"allowSharedKeyAccess":true,"defaultToOAuthAuthentication":false,"dnsEndpointType":"Standard","encryption":{"keySource":"Microsoft.Storage","requireInfrastructureEncryption":false,"services":{"blob":{"enabled":true,"keyType":"Account"},"file":{"enabled":true,"keyType":"Account"}}},"isHnsEnabled":true,"isLocalUserEnabled":true,"isNfsV3Enabled":true,"isSftpEnabled":true,"largeFileSharesState":"Enabled","minimumTlsVersion":"TLS1_2","networkAcls":{"bypass":"AzureServices","defaultAction":"Deny","ipRules":[]},"publicNetworkAccess":"Disabled","supportsHttpsTrafficOnly":true},"sku":{"name":"Standard_LRS","tier":"Standard"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "768"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-storage-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest?api-version=2023-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/providers/Microsoft.Storage/locations/chinaeast2/asyncoperations/********-0000-0000-0000-********0000?api-version=2023-05-01&c=c&h=h&monitor=true&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-storage-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/providers/Microsoft.Storage/locations/chinaeast2/asyncoperations/********-0000-0000-0000-********0000?api-version=2023-05-01&c=c&h=h&monitor=true&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1821
        uncompressed: false
        body: '{"sku":{"name":"Standard_LRS","tier":"Standard"},"kind":"StorageV2","id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest","name":"akscitfilesdktest","type":"Microsoft.Storage/storageAccounts","location":"chinaeast2","tags":{},"properties":{"dnsEndpointType":"Standard","defaultToOAuthAuthentication":false,"publicNetworkAccess":"Disabled","keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"allowCrossTenantReplication":false,"privateEndpointConnections":[],"isNfsV3Enabled":true,"isLocalUserEnabled":true,"isSftpEnabled":true,"minimumTlsVersion":"TLS1_2","allowBlobPublicAccess":false,"allowSharedKeyAccess":true,"largeFileSharesState":"Enabled","isHnsEnabled":true,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Deny"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"accessTier":"Cool","provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"dfs":"https://akscitfilesdktest.dfs.core.chinacloudapi.cn/","web":"https://akscitfilesdktest.z4.web.core.chinacloudapi.cn/","blob":"https://akscitfilesdktest.blob.core.chinacloudapi.cn/","queue":"https://akscitfilesdktest.queue.core.chinacloudapi.cn/","table":"https://akscitfilesdktest.table.core.chinacloudapi.cn/","file":"https://akscitfilesdktest.file.core.chinacloudapi.cn/"},"primaryLocation":"chinaeast2","statusOfPrimary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1821"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-FileServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest/fileServices/default?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 433
        uncompressed: false
        body: '{"sku":{"name":"Standard_LRS","tier":"Standard"},"id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest/fileServices/default","name":"default","type":"Microsoft.Storage/storageAccounts/fileServices","properties":{"protocolSettings":{"smb":{}},"cors":{"corsRules":[]},"shareDeleteRetentionPolicy":{"enabled":true,"days":7}}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "433"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-FileServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktestnotfound/fileServices/default?api-version=2023-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 352
        uncompressed: false
        body: '{"error":{"code":"ParentResourceNotFound","message":"Failed to perform ''read'' on resource(s) of type ''storageAccounts/fileServices'', because the parent resource ''/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktestnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "352"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 71
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"properties":{"shareDeleteRetentionPolicy":{"days":1,"enabled":true}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "71"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-FileServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest/fileServices/default?api-version=2023-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 331
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest/fileServices/default","name":"default","type":"Microsoft.Storage/storageAccounts/fileServices","properties":{"shareDeleteRetentionPolicy":{"enabled":true,"days":1}}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "331"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 71
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"properties":{"shareDeleteRetentionPolicy":{"days":1,"enabled":true}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "71"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-FileServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktestnotfound/fileServices/default?api-version=2023-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 353
        uncompressed: false
        body: '{"error":{"code":"ParentResourceNotFound","message":"Failed to perform ''write'' on resource(s) of type ''storageAccounts/fileServices'', because the parent resource ''/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktestnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "353"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-storage-client azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-FileServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitfilesdktest?api-version=2023-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-Azure-Storage-Resource-Provider/1.0,Microsoft-HTTPAPI/2.0 Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/resourcegroups/aks-cit-FileServiceProperties?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRGSUxFU0VSVklDRVBST1BFUlRJRVMtQ0hJTkFFQVNUMiIsImpvYkxvY2F0aW9uIjoiY2hpbmFlYXN0MiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.chinacloudapi.cn/subscriptions/********-0000-0000-0000-********0000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRGSUxFU0VSVklDRVBST1BFUlRJRVMtQ0hJTkFFQVNUMiIsImpvYkxvY2F0aW9uIjoiY2hpbmFlYXN0MiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
