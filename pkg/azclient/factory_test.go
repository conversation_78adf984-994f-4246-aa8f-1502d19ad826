// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package azclient

import (
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/cloud"
	"github.com/onsi/ginkgo/v2"
	"github.com/onsi/gomega"
)

var _ = ginkgo.Describe("Factory", func() {
	ginkgo.When("config is nil", func() {
		ginkgo.It("should create factory instance without painc - Account", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetAccountClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - AvailabilitySet", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetAvailabilitySetClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - BackendAddressPool", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetBackendAddressPoolClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - BlobContainer", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetBlobContainerClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - BlobServiceProperties", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetBlobServicePropertiesClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Deployment", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetDeploymentClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Disk", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetDiskClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - FileServiceProperties", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetFileServicePropertiesClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - FileShare", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetFileShareClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Identity", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetIdentityClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Interface", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetInterfaceClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - IPGroup", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetIPGroupClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - LoadBalancer", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetLoadBalancerClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - ManagedCluster", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetManagedClusterClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - PrivateDNSZoneGroup", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetPrivateDNSZoneGroupClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - PrivateEndpoint", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetPrivateEndpointClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - PrivateLinkService", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetPrivateLinkServiceClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - PrivateZone", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetPrivateZoneClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Provider", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetProviderClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - PublicIPAddress", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetPublicIPAddressClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - PublicIPPrefix", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetPublicIPPrefixClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Registry", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetRegistryClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - ResourceGroup", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetResourceGroupClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - RoleAssignment", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetRoleAssignmentClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - RouteTable", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetRouteTableClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Secret", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetSecretClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - SecurityGroup", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetSecurityGroupClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Snapshot", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetSnapshotClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - SSHPublicKeyResource", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetSSHPublicKeyResourceClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Subnet", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetSubnetClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - Vault", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetVaultClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - VirtualMachine", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetVirtualMachineClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - VirtualMachineScaleSet", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetVirtualMachineScaleSetClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - VirtualMachineScaleSetVM", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetVirtualMachineScaleSetVMClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - VirtualNetwork", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetVirtualNetworkClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
		ginkgo.It("should create factory instance without painc - VirtualNetworkLink", func() {
			factory, err := NewClientFactory(nil, nil, cloud.AzurePublic, nil)
			gomega.Expect(err).NotTo(gomega.HaveOccurred())
			gomega.Expect(factory).NotTo(gomega.BeNil())
			client := factory.GetVirtualNetworkLinkClient()
			gomega.Expect(client).NotTo(gomega.BeNil())
		})
	})
})
