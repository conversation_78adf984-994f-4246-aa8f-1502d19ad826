/*
Copyright 2023 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package virtualnetworkclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v6"
)

func (client *Client) CheckIPAddressAvailability(ctx context.Context, resourceGroupName string, virtualNetworkName string, ipAddress string) (*armnetwork.IPAddressAvailabilityResult, error) {
	resp, err := client.VirtualNetworksClient.CheckIPAddressAvailability(ctx, resourceGroupName, virtualNetworkName, ipAddress, nil)
	if err != nil {
		return nil, err
	}
	//handle statuscode
	return &resp.IPAddressAvailabilityResult, nil
}
