// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */
//

// Code generated by MockGen. DO NOT EDIT.
// Source: providerclient/interface.go
//
// Generated by this command:
//
//	mockgen -package mock_providerclient -source providerclient/interface.go -typed -write_generate_directive -copyright_file ../../hack/boilerplate/boilerplate.generatego.txt
//

// Package mock_providerclient is a generated GoMock package.
package mock_providerclient

import (
	context "context"
	reflect "reflect"

	armresources "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources"
	gomock "go.uber.org/mock/gomock"
)

//go:generate mockgen -package mock_providerclient -source providerclient/interface.go -typed -write_generate_directive -copyright_file ../../hack/boilerplate/boilerplate.generatego.txt

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
	isgomock struct{}
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetProvider mocks base method.
func (m *MockInterface) GetProvider(ctx context.Context, resourceProviderNamespace string) (*armresources.Provider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProvider", ctx, resourceProviderNamespace)
	ret0, _ := ret[0].(*armresources.Provider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProvider indicates an expected call of GetProvider.
func (mr *MockInterfaceMockRecorder) GetProvider(ctx, resourceProviderNamespace any) *MockInterfaceGetProviderCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProvider", reflect.TypeOf((*MockInterface)(nil).GetProvider), ctx, resourceProviderNamespace)
	return &MockInterfaceGetProviderCall{Call: call}
}

// MockInterfaceGetProviderCall wrap *gomock.Call
type MockInterfaceGetProviderCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockInterfaceGetProviderCall) Return(arg0 *armresources.Provider, arg1 error) *MockInterfaceGetProviderCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockInterfaceGetProviderCall) Do(f func(context.Context, string) (*armresources.Provider, error)) *MockInterfaceGetProviderCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockInterfaceGetProviderCall) DoAndReturn(f func(context.Context, string) (*armresources.Provider, error)) *MockInterfaceGetProviderCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetVirtualMachineSupportedZones mocks base method.
func (m *MockInterface) GetVirtualMachineSupportedZones(ctx context.Context) (map[string][]*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualMachineSupportedZones", ctx)
	ret0, _ := ret[0].(map[string][]*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualMachineSupportedZones indicates an expected call of GetVirtualMachineSupportedZones.
func (mr *MockInterfaceMockRecorder) GetVirtualMachineSupportedZones(ctx any) *MockInterfaceGetVirtualMachineSupportedZonesCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualMachineSupportedZones", reflect.TypeOf((*MockInterface)(nil).GetVirtualMachineSupportedZones), ctx)
	return &MockInterfaceGetVirtualMachineSupportedZonesCall{Call: call}
}

// MockInterfaceGetVirtualMachineSupportedZonesCall wrap *gomock.Call
type MockInterfaceGetVirtualMachineSupportedZonesCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockInterfaceGetVirtualMachineSupportedZonesCall) Return(arg0 map[string][]*string, arg1 error) *MockInterfaceGetVirtualMachineSupportedZonesCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockInterfaceGetVirtualMachineSupportedZonesCall) Do(f func(context.Context) (map[string][]*string, error)) *MockInterfaceGetVirtualMachineSupportedZonesCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockInterfaceGetVirtualMachineSupportedZonesCall) DoAndReturn(f func(context.Context) (map[string][]*string, error)) *MockInterfaceGetVirtualMachineSupportedZonesCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// ListProviders mocks base method.
func (m *MockInterface) ListProviders(ctx context.Context) ([]*armresources.Provider, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProviders", ctx)
	ret0, _ := ret[0].([]*armresources.Provider)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProviders indicates an expected call of ListProviders.
func (mr *MockInterfaceMockRecorder) ListProviders(ctx any) *MockInterfaceListProvidersCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProviders", reflect.TypeOf((*MockInterface)(nil).ListProviders), ctx)
	return &MockInterfaceListProvidersCall{Call: call}
}

// MockInterfaceListProvidersCall wrap *gomock.Call
type MockInterfaceListProvidersCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockInterfaceListProvidersCall) Return(arg0 []*armresources.Provider, arg1 error) *MockInterfaceListProvidersCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockInterfaceListProvidersCall) Do(f func(context.Context) ([]*armresources.Provider, error)) *MockInterfaceListProvidersCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockInterfaceListProvidersCall) DoAndReturn(f func(context.Context) ([]*armresources.Provider, error)) *MockInterfaceListProvidersCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}
