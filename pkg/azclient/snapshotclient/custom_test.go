// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package snapshotclient

import (
	"context"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armcompute "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v6"
	. "github.com/onsi/gomega"
)

var (
	computeClientFactory *armcompute.ClientFactory
	diskClient           *armcompute.DisksClient
)

var (
	diskName     string = "test-disk"
	diskResource *armcompute.Disk
)

func init() {
	additionalTestCases = func() {
	}

	beforeAllFunc = func(ctx context.Context) {
		storageClientOption := clientOption
		storageClientOption.Telemetry.ApplicationID = "ccm-storage-client"
		computeClientFactory, err = armcompute.NewClientFactory(subscriptionID, recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: storageClientOption,
		})
		Expect(err).NotTo(HaveOccurred())
		diskClient = computeClientFactory.NewDisksClient()
		diskPoller, err := diskClient.BeginCreateOrUpdate(ctx, resourceGroupName, diskName, armcompute.Disk{
			Location: to.Ptr(location),
			SKU: &armcompute.DiskSKU{
				Name: to.Ptr(armcompute.DiskStorageAccountTypesStandardLRS),
			},
			Properties: &armcompute.DiskProperties{
				CreationData: &armcompute.CreationData{
					CreateOption: to.Ptr(armcompute.DiskCreateOptionEmpty),
				},
				DiskSizeGB: to.Ptr[int32](64),
			},
		}, nil)
		Expect(err).NotTo(HaveOccurred())
		diskResp, err := diskPoller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
		diskResource = &diskResp.Disk
		newResource = &armcompute.Snapshot{
			Location: to.Ptr(location),
			Properties: &armcompute.SnapshotProperties{
				CreationData: &armcompute.CreationData{
					CreateOption:     to.Ptr(armcompute.DiskCreateOptionCopy),
					SourceResourceID: diskResource.ID,
				},
			},
		}
	}
	afterAllFunc = func(ctx context.Context) {
		diskPoller, err := diskClient.BeginDelete(ctx, resourceGroupName, diskName, nil)
		Expect(err).NotTo(HaveOccurred())
		_, err = diskPoller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
	}
}
