// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package snapshotclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/tracing"
	armcompute "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v6"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/metrics"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

const AzureStackCloudAPIVersion = "2019-03-01"

type Client struct {
	*armcompute.SnapshotsClient
	subscriptionID string
	tracer         tracing.Tracer
}

func New(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (Interface, error) {
	if options == nil {
		options = utils.GetDefaultOption()
	}
	tr := options.TracingProvider.NewTracer(utils.ModuleName, utils.ModuleVersion)

	client, err := armcompute.NewSnapshotsClient(subscriptionID, credential, options)
	if err != nil {
		return nil, err
	}
	return &Client{
		SnapshotsClient: client,
		subscriptionID:  subscriptionID,
		tracer:          tr,
	}, nil
}

const GetOperationName = "SnapshotsClient.Get"

// Get gets the Snapshot
func (client *Client) Get(ctx context.Context, resourceGroupName string, snapshotName string) (result *armcompute.Snapshot, err error) {

	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Snapshot", "get")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, GetOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := client.SnapshotsClient.Get(ctx, resourceGroupName, snapshotName, nil)
	if err != nil {
		return nil, err
	}
	//handle statuscode
	return &resp.Snapshot, nil
}

const CreateOrUpdateOperationName = "SnapshotsClient.Create"

// CreateOrUpdate creates or updates a Snapshot.
func (client *Client) CreateOrUpdate(ctx context.Context, resourceGroupName string, snapshotName string, resource armcompute.Snapshot) (result *armcompute.Snapshot, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Snapshot", "create_or_update")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, CreateOrUpdateOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := utils.NewPollerWrapper(client.SnapshotsClient.BeginCreateOrUpdate(ctx, resourceGroupName, snapshotName, resource, nil)).WaitforPollerResp(ctx)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &resp.Snapshot, nil
	}
	return nil, nil
}

const DeleteOperationName = "SnapshotsClient.Delete"

// Delete deletes a Snapshot by name.
func (client *Client) Delete(ctx context.Context, resourceGroupName string, snapshotName string) (err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "Snapshot", "delete")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, DeleteOperationName, client.tracer, nil)
	defer endSpan(err)
	_, err = utils.NewPollerWrapper(client.BeginDelete(ctx, resourceGroupName, snapshotName, nil)).WaitforPollerResp(ctx)
	return err
}
