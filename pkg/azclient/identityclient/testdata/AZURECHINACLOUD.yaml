---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-Identity?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 233
        uncompressed: false
        body: '{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-Identity","name":"aks-cit-Identity","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "233"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 47
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","name":"testResource"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "47"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-msi-client azsdk-go-armmsi/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource?api-version=2023-01-31
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 446
        uncompressed: false
        body: '{"location":"chinaeast2","tags":{},"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource","name":"testResource","type":"Microsoft.ManagedIdentity/userAssignedIdentities","properties":{"tenantId":"tenantid","principalId":"00000000-0000-0000-0000-000000000000","clientId":"00000000-0000-0000-0000-000000000000"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "446"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Location:
                - /subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource?c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Identity-client azsdk-go-armmsi/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource?api-version=2023-01-31
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 446
        uncompressed: false
        body: '{"location":"chinaeast2","tags":{},"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource","name":"testResource","type":"Microsoft.ManagedIdentity/userAssignedIdentities","properties":{"tenantId":"tenantid","principalId":"00000000-0000-0000-0000-000000000000","clientId":"00000000-0000-0000-0000-000000000000"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "446"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-Identity-client azsdk-go-armmsi/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResourcenotfound?api-version=2023-01-31
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 257
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.ManagedIdentity/userAssignedIdentities/testResourcenotfound'' under resource group ''aks-cit-Identity'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "257"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-msi-client azsdk-go-armmsi/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-Identity/providers/Microsoft.ManagedIdentity/userAssignedIdentities/testResource?api-version=2023-01-31
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-Identity?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRJREVOVElUWS1DSElOQUVBU1QyIiwiam9iTG9jYXRpb24iOiJjaGluYWVhc3QyIn0?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRJREVOVElUWS1DSElOQUVBU1QyIiwiam9iTG9jYXRpb24iOiJjaGluYWVhc3QyIn0?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
