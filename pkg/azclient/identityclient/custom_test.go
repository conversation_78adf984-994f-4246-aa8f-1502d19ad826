// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package identityclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/msi/armmsi"
	"github.com/onsi/gomega"
)

var msiClient *armmsi.UserAssignedIdentitiesClient
var useridentity *armmsi.Identity

func init() {
	additionalTestCases = func() {
	}

	beforeAllFunc = func(ctx context.Context) {
		var err error
		msiClientOption := clientOption
		msiClientOption.Telemetry.ApplicationID = "ccm-msi-client"
		msiClient, err = armmsi.NewUserAssignedIdentitiesClient(subscriptionID, recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: msiClientOption,
		})
		gomega.Expect(err).NotTo(gomega.HaveOccurred())

		resp, err := msiClient.CreateOrUpdate(ctx, resourceGroupName, resourceName, armmsi.Identity{
			Location: to.Ptr(location),
			Name:     to.Ptr(resourceName),
		}, nil)
		useridentity = &resp.Identity
		gomega.Expect(err).NotTo(gomega.HaveOccurred())

	}
	afterAllFunc = func(ctx context.Context) {
		_, err := msiClient.Delete(ctx, resourceGroupName, resourceName, nil)
		gomega.Expect(err).NotTo(gomega.HaveOccurred())
	}
}
