// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package virtualnetworklinkclient

import (
	"context"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armnetwork "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v6"
	armprivatedns "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/privatedns/armprivatedns"
	. "github.com/onsi/gomega"
)

var (
	networkClientFactory    *armnetwork.ClientFactory
	privatednsClientFactory *armprivatedns.ClientFactory
	privatednsClient        *armprivatedns.PrivateZonesClient
	virtualNetworksClient   *armnetwork.VirtualNetworksClient
	vNet                    *armnetwork.VirtualNetwork
)

func init() {
	additionalTestCases = func() {
	}

	beforeAllFunc = func(ctx context.Context) {
		dnsClientOption := clientOption
		dnsClientOption.Telemetry.ApplicationID = "ccm-privatedns-client"
		privatednsClientFactory, err = armprivatedns.NewClientFactory(subscriptionID, recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: dnsClientOption,
		})
		Expect(err).NotTo(HaveOccurred())
		privatezoneName = "aks-cit-privatednszone-cit.privatelink.global.azmk8s.io"
		privatednsClient = privatednsClientFactory.NewPrivateZonesClient()
		dnsPoller, err := privatednsClient.BeginCreateOrUpdate(ctx, resourceGroupName, privatezoneName, armprivatedns.PrivateZone{
			Location: to.Ptr("global"),
		}, nil)
		networkClientOption := clientOption
		networkClientOption.Telemetry.ApplicationID = "ccm-network-client"
		networkClientFactory, err := armnetwork.NewClientFactory(recorder.SubscriptionID(), recorder.TokenCredential(), &arm.ClientOptions{
			ClientOptions: networkClientOption,
		})
		Expect(err).NotTo(HaveOccurred())
		_, err = dnsPoller.PollUntilDone(ctx, nil)
		Expect(err).NotTo(HaveOccurred())

		virtualNetworksClient = networkClientFactory.NewVirtualNetworksClient()
		vnetpoller, err := virtualNetworksClient.BeginCreateOrUpdate(ctx, resourceGroupName, "vnet1", armnetwork.VirtualNetwork{
			Location: to.Ptr(location),
			Properties: &armnetwork.VirtualNetworkPropertiesFormat{
				AddressSpace: &armnetwork.AddressSpace{
					AddressPrefixes: []*string{
						to.Ptr("********/16"),
					},
				},
				Subnets: []*armnetwork.Subnet{
					{
						Name: to.Ptr("subnet1"),
						Properties: &armnetwork.SubnetPropertiesFormat{
							AddressPrefix: to.Ptr("********/24"),
						},
					},
				},
			},
		}, nil)
		Expect(err).NotTo(HaveOccurred())

		vnetresp, err := vnetpoller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
		Expect(err).NotTo(HaveOccurred())
		vNet = &vnetresp.VirtualNetwork
		newResource = &armprivatedns.VirtualNetworkLink{
			Location: to.Ptr("global"),
			Properties: &armprivatedns.VirtualNetworkLinkProperties{
				RegistrationEnabled: to.Ptr(false),
				VirtualNetwork: &armprivatedns.SubResource{
					ID: vNet.ID,
				},
			},
		}
	}
	afterAllFunc = func(ctx context.Context) {
		privatednsClient = privatednsClientFactory.NewPrivateZonesClient()
		dnsPoller, err := privatednsClient.BeginDelete(ctx, resourceGroupName, privatezoneName, nil)
		Expect(err).NotTo(HaveOccurred())
		_, err = dnsPoller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
		vnetPoller, err := virtualNetworksClient.BeginDelete(ctx, resourceGroupName, *vNet.Name, nil)
		Expect(err).NotTo(HaveOccurred())
		_, err = vnetPoller.PollUntilDone(ctx, &runtime.PollUntilDoneOptions{
			Frequency: 1 * time.Second,
		})
		Expect(err).NotTo(HaveOccurred())
	}
}
