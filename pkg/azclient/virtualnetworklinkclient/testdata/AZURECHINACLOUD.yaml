---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-VirtualNetworkLink?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 253
        uncompressed: false
        body: '{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink","name":"aks-cit-VirtualNetworkLink","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "253"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 21
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"global"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "21"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io?api-version=2024-06-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 2
        uncompressed: false
        body: '{}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRQcml2YXRlRG5zWm9uZTtkNzQ3NGYwNi02ZDk3LTRlMDMtYjIxOS00NjVlNDNhNTU1YzhfMmFhZGZjOTUtMWM0MC00NzgyLTk5NTgtMDdjMmU5OWZlMTk2?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "2"
            Content-Type:
                - application/json; charset=utf-8
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRQcml2YXRlRG5zWm9uZTtkNzQ3NGYwNi02ZDk3LTRlMDMtYjIxOS00NjVlNDNhNTU1YzhfMmFhZGZjOTUtMWM0MC00NzgyLTk5NTgtMDdjMmU5OWZlMTk2?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRQcml2YXRlRG5zWm9uZTtkNzQ3NGYwNi02ZDk3LTRlMDMtYjIxOS00NjVlNDNhNTU1YzhfMmFhZGZjOTUtMWM0MC00NzgyLTk5NTgtMDdjMmU5OWZlMTk2?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 752
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io","name":"aks-cit-privatednszone-cit.privatelink.global.azmk8s.io","type":"Microsoft.Network\/privateDnsZones","etag":"00000000-0000-0000-0000-000000000000","location":"global","properties":{"internalId":"SW1tdXRhYmxlWm9uZUlkZW50aXR5O2JjNzRjYWJmLThmODItNGJmYS05NDYxLWRiYjk2NjIxMTk0Mzsw","maxNumberOfRecordSets":25000,"maxNumberOfVirtualNetworkLinks":1000,"maxNumberOfVirtualNetworkLinksWithRegistration":100,"numberOfRecordSets":1,"numberOfVirtualNetworkLinks":0,"numberOfVirtualNetworkLinksWithRegistration":0,"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "752"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - 00000000-0000-0000-0000-000000000000
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 167
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","properties":{"addressSpace":{"addressPrefixes":["********/16"]},"subnets":[{"name":"subnet1","properties":{"addressPrefix":"********/24"}}]}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "167"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1019
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/virtualNetworks","location":"chinaeast2","properties":{"provisioningState":"Updating","resourceGuid":"00000000-0000-0000-0000-000000000000","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","properties":{"provisioningState":"Updating","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Enabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1019"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1021
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/virtualNetworks","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","properties":{"provisioningState":"Succeeded","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Enabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1021"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 234
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"global","properties":{"registrationEnabled":false,"virtualNetwork":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1"}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "234"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 2
        uncompressed: false
        body: '{}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7ZDg5M2Q4M2UtMTVjNS00Y2IyLTlmMDEtYmFhZDEyNWNkMTkxXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "2"
            Content-Type:
                - application/json; charset=utf-8
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7ZDg5M2Q4M2UtMTVjNS00Y2IyLTlmMDEtYmFhZDEyNWNkMTkxXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7ZDg5M2Q4M2UtMTVjNS00Y2IyLTlmMDEtYmFhZDEyNWNkMTkxXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 722
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "722"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"00000000-0000-0000-0000-000000000000"'
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 722
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "722"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"00000000-0000-0000-0000-000000000000"'
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResourcenotfound?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 328
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResourcenotfound'' under resource group ''aks-cit-VirtualNetworkLink'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "328"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 234
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"global","properties":{"registrationEnabled":false,"virtualNetwork":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1"}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "234"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 2
        uncompressed: false
        body: '{}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7OWY2YjM4OGYtOTBiMy00MWVjLTgxMDktZTI5YTVmNjkyYzVjXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "2"
            Content-Type:
                - application/json; charset=utf-8
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7OWY2YjM4OGYtOTBiMy00MWVjLTgxMDktZTI5YTVmNjkyYzVjXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 13
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7OWY2YjM4OGYtOTBiMy00MWVjLTgxMDktZTI5YTVmNjkyYzVjXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 14
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 722
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "722"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"00000000-0000-0000-0000-000000000000"'
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 15
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 734
        uncompressed: false
        body: '{"value":[{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}]}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "734"
            Content-Type:
                - application/json; charset=utf-8
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Resource-Entities-Read:
                - "59999"
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 16
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLinknotfound/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 126
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-VirtualNetworkLinknotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "126"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 17
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVWaXJ0dWFsTmV0d29ya0xpbms7OTYwYmNjYTctOGNlYi00NTI0LWJjNmQtMzM5MTcwYmJkNDljXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "0"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVWaXJ0dWFsTmV0d29ya0xpbms7OTYwYmNjYTctOGNlYi00NTI0LWJjNmQtMzM5MTcwYmJkNDljXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 18
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVWaXJ0dWFsTmV0d29ya0xpbms7OTYwYmNjYTctOGNlYi00NTI0LWJjNmQtMzM5MTcwYmJkNDljXzJhYWRmYzk1LTFjNDAtNDc4Mi05OTU4LTA3YzJlOTlmZTE5Ng==?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 19
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io?api-version=2024-06-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVQcml2YXRlRG5zWm9uZTtlOTgyYzRhMi05ODg3LTQzNzAtYjc4MC1lMzJiZmM4ZTE4N2JfMmFhZGZjOTUtMWM0MC00NzgyLTk5NTgtMDdjMmU5OWZlMTk2?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "0"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVQcml2YXRlRG5zWm9uZTtlOTgyYzRhMi05ODg3LTQzNzAtYjc4MC1lMzJiZmM4ZTE4N2JfMmFhZGZjOTUtMWM0MC00NzgyLTk5NTgtMDdjMmU5OWZlMTk2?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 20
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVQcml2YXRlRG5zWm9uZTtlOTgyYzRhMi05ODg3LTQzNzAtYjc4MC1lMzJiZmM4ZTE4N2JfMmFhZGZjOTUtMWM0MC00NzgyLTk5NTgtMDdjMmU5OWZlMTk2?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Server:
                - Microsoft-IIS/10.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 21
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 22
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/chinaeast2/00000000-0000-0000-0000-000000000000
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 23
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-VirtualNetworkLink?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRWSVJUVUFMTkVUV09SS0xJTkstQ0hJTkFFQVNUMiIsImpvYkxvY2F0aW9uIjoiY2hpbmFlYXN0MiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 24
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.2; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRWSVJUVUFMTkVUV09SS0xJTkstQ0hJTkFFQVNUMiIsImpvYkxvY2F0aW9uIjoiY2hpbmFlYXN0MiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
