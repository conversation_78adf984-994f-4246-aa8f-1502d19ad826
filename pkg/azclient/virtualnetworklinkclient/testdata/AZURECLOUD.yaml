---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 22
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "22"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-VirtualNetworkLink?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 250
        uncompressed: false
        body: '{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink","name":"aks-cit-VirtualNetworkLink","type":"Microsoft.Resources/resourceGroups","location":"eastus2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "250"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 21
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"global"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "21"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io?api-version=2024-06-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 2
        uncompressed: false
        body: '{}'
        headers:
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRQcml2YXRlRG5zWm9uZTthNzBkMTMyYS0yMTc1LTRhMTAtOTZjMy0xZTUzYWVmZWFjOWJfMjZhZDkwM2YtMjMzMC00MjlkLTgzODktODY0YWMzNWM0MzUw?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "2"
            Content-Type:
                - application/json; charset=utf-8
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRQcml2YXRlRG5zWm9uZTthNzBkMTMyYS0yMTc1LTRhMTAtOTZjMy0xZTUzYWVmZWFjOWJfMjZhZDkwM2YtMjMzMC00MjlkLTgzODktODY0YWMzNWM0MzUw?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRQcml2YXRlRG5zWm9uZTthNzBkMTMyYS0yMTc1LTRhMTAtOTZjMy0xZTUzYWVmZWFjOWJfMjZhZDkwM2YtMjMzMC00MjlkLTgzODktODY0YWMzNWM0MzUw?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 752
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io","name":"aks-cit-privatednszone-cit.privatelink.global.azmk8s.io","type":"Microsoft.Network\/privateDnsZones","etag":"00000000-0000-0000-0000-000000000000","location":"global","properties":{"internalId":"SW1tdXRhYmxlWm9uZUlkZW50aXR5OzRlZTM3NmZkLTQ1YTItNDdmYy05NGJhLTgwNGUyOTg2NjgxYzsw","maxNumberOfRecordSets":25000,"maxNumberOfVirtualNetworkLinks":1000,"maxNumberOfVirtualNetworkLinksWithRegistration":100,"numberOfRecordSets":1,"numberOfVirtualNetworkLinks":0,"numberOfVirtualNetworkLinksWithRegistration":0,"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "752"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - 00000000-0000-0000-0000-000000000000
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 164
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus2","properties":{"addressSpace":{"addressPrefixes":["********/16"]},"subnets":[{"name":"subnet1","properties":{"addressPrefix":"********/24"}}]}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "164"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1016
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/virtualNetworks","location":"eastus2","properties":{"provisioningState":"Updating","resourceGuid":"00000000-0000-0000-0000-000000000000","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","properties":{"provisioningState":"Updating","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Enabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1016"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1018
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/virtualNetworks","location":"eastus2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","properties":{"provisioningState":"Succeeded","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Enabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1018"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 234
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"global","properties":{"registrationEnabled":false,"virtualNetwork":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1"}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "234"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 2
        uncompressed: false
        body: '{}'
        headers:
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7OGYyNmEzZWQtMzdlMC00ZTc4LWIzN2QtOWQ4N2NjZTQyNDliXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "2"
            Content-Type:
                - application/json; charset=utf-8
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7OGYyNmEzZWQtMzdlMC00ZTc4LWIzN2QtOWQ4N2NjZTQyNDliXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7OGYyNmEzZWQtMzdlMC00ZTc4LWIzN2QtOWQ4N2NjZTQyNDliXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 722
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "722"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"00000000-0000-0000-0000-000000000000"'
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 722
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "722"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"00000000-0000-0000-0000-000000000000"'
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResourcenotfound?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 328
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResourcenotfound'' under resource group ''aks-cit-VirtualNetworkLink'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "328"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 234
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"global","properties":{"registrationEnabled":false,"virtualNetwork":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1"}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "234"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 2
        uncompressed: false
        body: '{}'
        headers:
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7YzU0N2U2OTItYWUyNy00MmU3LWFmZTgtZTM0ZTJjNTkzNGJjXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "2"
            Content-Type:
                - application/json; charset=utf-8
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7YzU0N2U2OTItYWUyNy00MmU3LWFmZTgtZTM0ZTJjNTkzNGJjXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 13
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtVcHNlcnRWaXJ0dWFsTmV0d29ya0xpbms7YzU0N2U2OTItYWUyNy00MmU3LWFmZTgtZTM0ZTJjNTkzNGJjXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 14
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 722
        uncompressed: false
        body: '{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-virtualnetworklink\/providers\/Microsoft.Network\/privateDnsZones\/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io\/virtualNetworkLinks\/testresource","name":"testresource","type":"Microsoft.Network\/privateDnsZones\/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"\/subscriptions\/00000000-0000-0000-0000-000000000000\/resourceGroups\/aks-cit-VirtualNetworkLink\/providers\/Microsoft.Network\/virtualNetworks\/vnet1"},"virtualNetworkLinkState":"Completed"}}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "722"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"00000000-0000-0000-0000-000000000000"'
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 15
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 714
        uncompressed: false
        body: '{"value":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testresource","name":"testresource","type":"Microsoft.Network/privateDnsZones/virtualNetworkLinks","etag":"\"00000000-0000-0000-0000-000000000000\"","location":"global","properties":{"provisioningState":"Succeeded","registrationEnabled":false,"resolutionPolicy":"Default","virtualNetwork":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1"},"virtualNetworkLinkState":"Completed"}}]}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "714"
            Content-Type:
                - application/json; charset=utf-8
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Original-Request-Ids:
                - 00000000-0000-0000-0000-000000000000
            X-Ms-Ratelimit-Remaining-Subscription-Resource-Entities-Read:
                - "60000"
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 16
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLinknotfound/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks?api-version=2024-06-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 126
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-VirtualNetworkLinknotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "126"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 17
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io/virtualNetworkLinks/testResource?api-version=2024-06-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVWaXJ0dWFsTmV0d29ya0xpbms7MzM2YmZkMjgtZmQxYy00ODk4LThhNTEtOWFjYTdiNWNjMWUyXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "0"
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVWaXJ0dWFsTmV0d29ya0xpbms7MzM2YmZkMjgtZmQxYy00ODk4LThhNTEtOWFjYTdiNWNjMWUyXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 18
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualNetworkLink-c azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVWaXJ0dWFsTmV0d29ya0xpbms7MzM2YmZkMjgtZmQxYy00ODk4LThhNTEtOWFjYTdiNWNjMWUyXzI2YWQ5MDNmLTIzMzAtNDI5ZC04Mzg5LTg2NGFjMzVjNDM1MA==?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 19
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/privateDnsZones/aks-cit-privatednszone-cit.privatelink.global.azmk8s.io?api-version=2024-06-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVQcml2YXRlRG5zWm9uZTtjNDg5Zjk0MC1hZWMwLTRhMDYtODFmNy0yNDlmMDUzMTY5N2NfMjZhZDkwM2YtMjMzMC00MjlkLTgzODktODY0YWMzNWM0MzUw?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - private
            Content-Length:
                - "0"
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationResults/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVQcml2YXRlRG5zWm9uZTtjNDg5Zjk0MC1hZWMwLTRhMDYtODFmNy0yNDlmMDUzMTY5N2NfMjZhZDkwM2YtMjMzMC00MjlkLTgzODktODY0YWMzNWM0MzUw?api-version=2024-06-01&c=c&h=h&s=s&t=t
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 20
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-privatedns-client azsdk-go-armprivatedns/v1.3.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-virtualnetworklink/providers/Microsoft.Network/privateDnsOperationStatuses/RnJvbnRFbmRBc3luY09wZXJhdGlvbjtEZWxldGVQcml2YXRlRG5zWm9uZTtjNDg5Zjk0MC1hZWMwLTRhMDYtODFmNy0yNDlmMDUzMTY5N2NfMjZhZDkwM2YtMjMzMC00MjlkLTgzODktODY0YWMzNWM0MzUw?api-version=2024-06-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - private
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Aspnet-Version:
                - 4.0.30319
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Powered-By:
                - ASP.NET
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 21
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-VirtualNetworkLink/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus2/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 22
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Operation-Identifier:
                - tenantId=00000000-0000-0000-0000-000000000000,objectId=00000000-0000-0000-0000-000000000000/japaneast/00000000-0000-0000-0000-000000000000
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 23
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-VirtualNetworkLink?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRWSVJUVUFMTkVUV09SS0xJTkstRUFTVFVTMiIsImpvYkxvY2F0aW9uIjoiZWFzdHVzMiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 24
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRWSVJUVUFMTkVUV09SS0xJTkstRUFTVFVTMiIsImpvYkxvY2F0aW9uIjoiZWFzdHVzMiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
