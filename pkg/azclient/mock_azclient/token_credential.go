// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */
//

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/Azure/azure-sdk-for-go/sdk/azcore (interfaces: TokenCredential)
//
// Generated by this command:
//
//	mockgen -copyright_file ../../hack/boilerplate/boilerplate.generatego.txt -package mock_azclient github.com/Azure/azure-sdk-for-go/sdk/azcore TokenCredential
//

// Package mock_azclient is a generated GoMock package.
package mock_azclient

import (
	context "context"
	reflect "reflect"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	gomock "go.uber.org/mock/gomock"
)

// MockTokenCredential is a mock of TokenCredential interface.
type MockTokenCredential struct {
	ctrl     *gomock.Controller
	recorder *MockTokenCredentialMockRecorder
}

// MockTokenCredentialMockRecorder is the mock recorder for MockTokenCredential.
type MockTokenCredentialMockRecorder struct {
	mock *MockTokenCredential
}

// NewMockTokenCredential creates a new mock instance.
func NewMockTokenCredential(ctrl *gomock.Controller) *MockTokenCredential {
	mock := &MockTokenCredential{ctrl: ctrl}
	mock.recorder = &MockTokenCredentialMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenCredential) EXPECT() *MockTokenCredentialMockRecorder {
	return m.recorder
}

// GetToken mocks base method.
func (m *MockTokenCredential) GetToken(arg0 context.Context, arg1 policy.TokenRequestOptions) (azcore.AccessToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToken", arg0, arg1)
	ret0, _ := ret[0].(azcore.AccessToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToken indicates an expected call of GetToken.
func (mr *MockTokenCredentialMockRecorder) GetToken(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToken", reflect.TypeOf((*MockTokenCredential)(nil).GetToken), arg0, arg1)
}
