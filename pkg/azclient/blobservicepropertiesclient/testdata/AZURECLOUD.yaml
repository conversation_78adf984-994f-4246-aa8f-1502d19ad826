---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 21
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "21"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourcegroups/aks-cit-BlobServiceProperties?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 255
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties","name":"aks-cit-BlobServiceProperties","type":"Microsoft.Resources/resourceGroups","location":"eastus","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "255"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 764
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"kind":"StorageV2","location":"eastus","properties":{"accessTier":"Cool","allowBlobPublicAccess":false,"allowCrossTenantReplication":false,"allowSharedKeyAccess":true,"defaultToOAuthAuthentication":false,"dnsEndpointType":"Standard","encryption":{"keySource":"Microsoft.Storage","requireInfrastructureEncryption":false,"services":{"blob":{"enabled":true,"keyType":"Account"},"file":{"enabled":true,"keyType":"Account"}}},"isHnsEnabled":true,"isLocalUserEnabled":true,"isNfsV3Enabled":true,"isSftpEnabled":true,"largeFileSharesState":"Enabled","minimumTlsVersion":"TLS1_2","networkAcls":{"bypass":"AzureServices","defaultAction":"Deny","ipRules":[]},"publicNetworkAccess":"Disabled","supportsHttpsTrafficOnly":true},"sku":{"name":"Standard_LRS","tier":"Standard"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "764"
            Content-Type:
                - application/json
            User-Agent:
                - azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest?api-version=2024-01-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/providers/Microsoft.Storage/locations/eastus/asyncoperations/********-0000-0000-0000-********0000?api-version=2024-01-01&c=c&h=h&monitor=true&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/providers/Microsoft.Storage/locations/eastus/asyncoperations/********-0000-0000-0000-********0000?api-version=2024-01-01&c=c&h=h&monitor=true&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1784
        uncompressed: false
        body: '{"sku":{"name":"Standard_LRS","tier":"Standard"},"kind":"StorageV2","id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest","name":"akscitblobsdktest","type":"Microsoft.Storage/storageAccounts","location":"eastus","tags":{},"properties":{"dnsEndpointType":"Standard","defaultToOAuthAuthentication":false,"publicNetworkAccess":"Disabled","keyCreationTime":{"key1":"2001-02-03T04:05:06Z","key2":"2001-02-03T04:05:06Z"},"allowCrossTenantReplication":false,"privateEndpointConnections":[],"isNfsV3Enabled":true,"isLocalUserEnabled":true,"isSftpEnabled":true,"minimumTlsVersion":"TLS1_2","allowBlobPublicAccess":false,"allowSharedKeyAccess":true,"largeFileSharesState":"Enabled","isHnsEnabled":true,"networkAcls":{"ipv6Rules":[],"bypass":"AzureServices","virtualNetworkRules":[],"ipRules":[],"defaultAction":"Deny"},"supportsHttpsTrafficOnly":true,"encryption":{"requireInfrastructureEncryption":false,"services":{"file":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"},"blob":{"keyType":"Account","enabled":true,"lastEnabledTime":"2001-02-03T04:05:06Z"}},"keySource":"Microsoft.Storage"},"accessTier":"Cool","provisioningState":"Succeeded","creationTime":"2001-02-03T04:05:06Z","primaryEndpoints":{"dfs":"https://akscitblobsdktest.dfs.core.windows.net/","web":"https://akscitblobsdktest.z13.web.core.windows.net/","blob":"https://akscitblobsdktest.blob.core.windows.net/","queue":"https://akscitblobsdktest.queue.core.windows.net/","table":"https://akscitblobsdktest.table.core.windows.net/","file":"https://akscitblobsdktest.file.core.windows.net/"},"primaryLocation":"eastus","statusOfPrimary":"available"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1784"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-BlobServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest/blobServices/default?api-version=2024-01-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 419
        uncompressed: false
        body: '{"sku":{"name":"Standard_LRS","tier":"Standard"},"id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest/blobServices/default","name":"default","type":"Microsoft.Storage/storageAccounts/blobServices","properties":{"cors":{"corsRules":[]},"deleteRetentionPolicy":{"allowPermanentDelete":false,"enabled":false}}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "419"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-BlobServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestnotfound/blobServices/default?api-version=2024-01-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 352
        uncompressed: false
        body: '{"error":{"code":"ParentResourceNotFound","message":"Failed to perform ''read'' on resource(s) of type ''storageAccounts/blobServices'', because the parent resource ''/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "352"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 66
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"properties":{"deleteRetentionPolicy":{"days":1,"enabled":true}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "66"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-BlobServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest/blobServices/default?api-version=2024-01-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 326
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest/blobServices/default","name":"default","type":"Microsoft.Storage/storageAccounts/blobServices","properties":{"deleteRetentionPolicy":{"enabled":true,"days":1}}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "326"
            Content-Type:
                - application/json
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 66
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"properties":{"deleteRetentionPolicy":{"days":1,"enabled":true}}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "66"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-BlobServicePropertie azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestnotfound/blobServices/default?api-version=2024-01-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 353
        uncompressed: false
        body: '{"error":{"code":"ParentResourceNotFound","message":"Failed to perform ''write'' on resource(s) of type ''storageAccounts/blobServices'', because the parent resource ''/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktestnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "353"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - azsdk-go-armstorage/v1.7.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourceGroups/aks-cit-BlobServiceProperties/providers/Microsoft.Storage/storageAccounts/akscitblobsdktest?api-version=2024-01-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Content-Type:
                - text/plain; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "12000"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/resourcegroups/aks-cit-BlobServiceProperties?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRCTE9CU0VSVklDRVBST1BFUlRJRVMtRUFTVFVTIiwiam9iTG9jYXRpb24iOiJlYXN0dXMifQ?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.4; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-********0000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRCTE9CU0VSVklDRVBST1BFUlRJRVMtRUFTVFVTIiwiam9iTG9jYXRpb24iOiJlYXN0dXMifQ?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=********; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
