---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 25
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "25"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-PrivateLinkService?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 253
        uncompressed: false
        body: '{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService","name":"aks-cit-PrivateLinkService","type":"Microsoft.Resources/resourceGroups","location":"chinaeast2","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "253"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 152
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","properties":{"publicIPAddressVersion":"IPv4","publicIPAllocationMethod":"Static"},"sku":{"name":"Standard","tier":"Regional"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "152"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 597
        uncompressed: false
        body: '{"name":"pip1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","location":"chinaeast2","properties":{"provisioningState":"Updating","resourceGuid":"00000000-0000-0000-0000-000000000000","publicIPAddressVersion":"IPv4","publicIPAllocationMethod":"Static","idleTimeoutInMinutes":4,"ipTags":[],"ddosSettings":{"protectionMode":"VirtualNetworkInherited"}},"type":"Microsoft.Network/publicIPAddresses","sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "597"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 625
        uncompressed: false
        body: '{"name":"pip1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","ipAddress":"************","publicIPAddressVersion":"IPv4","publicIPAllocationMethod":"Static","idleTimeoutInMinutes":4,"ipTags":[],"ddosSettings":{"protectionMode":"VirtualNetworkInherited"}},"type":"Microsoft.Network/publicIPAddresses","sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "625"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 799
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","properties":{"frontendIPConfigurations":[{"name":"frontendConfig1","properties":{"publicIPAddress":{"etag":"W/\"00000000-0000-0000-0000-000000000000\"","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1","location":"chinaeast2","name":"pip1","properties":{"ddosSettings":{"protectionMode":"VirtualNetworkInherited"},"idleTimeoutInMinutes":4,"ipAddress":"************","ipTags":[],"provisioningState":"Succeeded","publicIPAddressVersion":"IPv4","publicIPAllocationMethod":"Static","resourceGuid":"00000000-0000-0000-0000-000000000000"},"sku":{"name":"Standard","tier":"Regional"},"type":"Microsoft.Network/publicIPAddresses"}}}]},"sku":{"name":"Standard","tier":"Regional"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "799"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1164
        uncompressed: false
        body: '{"name":"lb1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/loadBalancers","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","frontendIPConfigurations":[{"name":"frontendConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/loadBalancers/frontendIPConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","publicIPAddress":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1"}}}],"backendAddressPools":[],"loadBalancingRules":[],"probes":[],"inboundNatRules":[],"outboundRules":[],"inboundNatPools":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1164"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1164
        uncompressed: false
        body: '{"name":"lb1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/loadBalancers","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","frontendIPConfigurations":[{"name":"frontendConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/loadBalancers/frontendIPConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","publicIPAddress":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1"}}}],"backendAddressPools":[],"loadBalancingRules":[],"probes":[],"inboundNatRules":[],"outboundRules":[],"inboundNatPools":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1164"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 258
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","properties":{"addressSpace":{"addressPrefixes":["********/16"]},"subnets":[{"name":"subnet1","properties":{"addressPrefix":"********/24","privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Disabled"}}]}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "258"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1020
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/virtualNetworks","location":"chinaeast2","properties":{"provisioningState":"Updating","resourceGuid":"00000000-0000-0000-0000-000000000000","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","properties":{"provisioningState":"Updating","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Disabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1020"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1022
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/virtualNetworks","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","properties":{"provisioningState":"Succeeded","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Disabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1022"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 1259
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","properties":{"ipConfigurations":[{"name":"ipConfig1","properties":{"primary":true,"privateIPAddressVersion":"IPv4","subnet":{"etag":"W/\"00000000-0000-0000-0000-000000000000\"","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","name":"subnet1","properties":{"addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Disabled","provisioningState":"Succeeded"},"type":"Microsoft.Network/virtualNetworks/subnets"}}}],"loadBalancerFrontendIpConfigurations":[{"etag":"W/\"00000000-0000-0000-0000-000000000000\"","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1","name":"frontendConfig1","properties":{"privateIPAllocationMethod":"Dynamic","provisioningState":"Succeeded","publicIPAddress":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1"}},"type":"Microsoft.Network/loadBalancers/frontendIPConfigurations"}]}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "1259"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1689
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices","location":"chinaeast2","properties":{"provisioningState":"Updating","resourceGuid":"00000000-0000-0000-0000-000000000000","fqdns":[],"alias":"testresource.00000000-0000-0000-0000-000000000000.chinaeast2.azure.privatelinkservice","enableProxyProtocol":false,"loadBalancerFrontendIpConfigurations":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1"}],"ipConfigurations":[{"name":"ipConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource/ipConfigurations/ipConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices/ipConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","subnet":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"},"primary":true,"privateIPAddressVersion":"IPv4"}}],"privateEndpointConnections":[],"networkInterfaces":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/networkInterfaces/testResource.nic.00000000-0000-0000-0000-000000000000"}]}}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1689"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1690
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","fqdns":[],"alias":"testresource.00000000-0000-0000-0000-000000000000.chinaeast2.azure.privatelinkservice","enableProxyProtocol":false,"loadBalancerFrontendIpConfigurations":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1"}],"ipConfigurations":[{"name":"ipConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource/ipConfigurations/ipConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices/ipConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","subnet":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"},"primary":true,"privateIPAddressVersion":"IPv4"}}],"privateEndpointConnections":[],"networkInterfaces":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/networkInterfaces/testResource.nic.00000000-0000-0000-0000-000000000000"}]}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1690"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1690
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","fqdns":[],"alias":"testresource.00000000-0000-0000-0000-000000000000.chinaeast2.azure.privatelinkservice","enableProxyProtocol":false,"loadBalancerFrontendIpConfigurations":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1"}],"ipConfigurations":[{"name":"ipConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource/ipConfigurations/ipConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices/ipConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","subnet":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"},"primary":true,"privateIPAddressVersion":"IPv4"}}],"privateEndpointConnections":[],"networkInterfaces":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/networkInterfaces/testResource.nic.00000000-0000-0000-0000-000000000000"}]}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1690"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 13
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResourcenotfound?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 256
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.Network/privateLinkServices/testResourcenotfound'' under resource group ''aks-cit-PrivateLinkService'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "256"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 14
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 1259
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: '{"location":"chinaeast2","properties":{"ipConfigurations":[{"name":"ipConfig1","properties":{"primary":true,"privateIPAddressVersion":"IPv4","subnet":{"etag":"W/\"00000000-0000-0000-0000-000000000000\"","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","name":"subnet1","properties":{"addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Disabled","provisioningState":"Succeeded"},"type":"Microsoft.Network/virtualNetworks/subnets"}}}],"loadBalancerFrontendIpConfigurations":[{"etag":"W/\"00000000-0000-0000-0000-000000000000\"","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1","name":"frontendConfig1","properties":{"privateIPAllocationMethod":"Dynamic","provisioningState":"Succeeded","publicIPAddress":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1"}},"type":"Microsoft.Network/loadBalancers/frontendIPConfigurations"}]}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "1259"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1690
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","fqdns":[],"alias":"testresource.00000000-0000-0000-0000-000000000000.chinaeast2.azure.privatelinkservice","enableProxyProtocol":false,"loadBalancerFrontendIpConfigurations":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1"}],"ipConfigurations":[{"name":"ipConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource/ipConfigurations/ipConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices/ipConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","subnet":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"},"primary":true,"privateIPAddressVersion":"IPv4"}}],"privateEndpointConnections":[],"networkInterfaces":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/networkInterfaces/testResource.nic.00000000-0000-0000-0000-000000000000"}]}}'
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1690"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 15
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1690
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","fqdns":[],"alias":"testresource.00000000-0000-0000-0000-000000000000.chinaeast2.azure.privatelinkservice","enableProxyProtocol":false,"loadBalancerFrontendIpConfigurations":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1"}],"ipConfigurations":[{"name":"ipConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource/ipConfigurations/ipConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices/ipConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","subnet":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"},"primary":true,"privateIPAddressVersion":"IPv4"}}],"privateEndpointConnections":[],"networkInterfaces":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/networkInterfaces/testResource.nic.00000000-0000-0000-0000-000000000000"}]}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1690"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 16
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1702
        uncompressed: false
        body: '{"value":[{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices","location":"chinaeast2","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","fqdns":[],"alias":"testresource.00000000-0000-0000-0000-000000000000.chinaeast2.azure.privatelinkservice","enableProxyProtocol":false,"loadBalancerFrontendIpConfigurations":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1/frontendIPConfigurations/frontendConfig1"}],"ipConfigurations":[{"name":"ipConfig1","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource/ipConfigurations/ipConfig1","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/privateLinkServices/ipConfigurations","properties":{"provisioningState":"Succeeded","privateIPAllocationMethod":"Dynamic","subnet":{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"},"primary":true,"privateIPAddressVersion":"IPv4"}}],"privateEndpointConnections":[],"networkInterfaces":[{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/networkInterfaces/testResource.nic.00000000-0000-0000-0000-000000000000"}]}}]}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1702"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 17
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkServicenotfound/providers/Microsoft.Network/privateLinkServices?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 126
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-PrivateLinkServicenotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "126"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 18
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/privateLinkServices/testResource?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 19
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PrivateLinkService-c azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 20
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/loadBalancers/lb1?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 21
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 22
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/publicIPAddresses/pip1?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 23
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 24
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PrivateLinkService/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncoperation:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 25
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-network-client azsdk-go-armnetwork/v6.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/chinaeast2/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Server:
                - Microsoft-HTTPAPI/2.0
                - Microsoft-HTTPAPI/2.0
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 26
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-PrivateLinkService?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRQUklWQVRFTElOS1NFUlZJQ0UtQ0hJTkFFQVNUMiIsImpvYkxvY2F0aW9uIjoiY2hpbmFlYXN0MiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 27
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.chinacloudapi.cn
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.24.0; linux)
        url: https://management.chinacloudapi.cn/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRQUklWQVRFTElOS1NFUlZJQ0UtQ0hJTkFFQVNUMiIsImpvYkxvY2F0aW9uIjoiY2hpbmFlYXN0MiJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Content-Type-Options:
                - nosniff
        status: 200 OK
        code: 200
        duration: 200ms
