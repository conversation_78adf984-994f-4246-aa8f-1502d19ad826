// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package roledefinitionclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/authorization/armauthorization/v2"
	"github.com/onsi/ginkgo/v2"
	"github.com/onsi/gomega"
)

func init() {
	additionalTestCases = func() {
		ginkgo.When("list requests are raised", func() {
			ginkgo.It("should not return error", func(ctx context.Context) {
				resourceList, err := realClient.List(ctx, "/subscriptions/"+subscriptionID+"/resourceGroups/"+resourceGroupName, &armauthorization.RoleDefinitionsClientListOptions{
					Filter: to.Ptr("roleName eq 'Contributor'"),
				})
				gomega.Expect(err).NotTo(gomega.HaveOccurred())
				gomega.Expect(resourceList).NotTo(gomega.BeNil())
				gomega.Expect(len(resourceList)).To(gomega.Equal(1))
				gomega.Expect(*resourceList[0].Properties.RoleName).To(gomega.Equal("Contributor"))
			})
		})
		ginkgo.When("invalid list requests are raised", func() {
			ginkgo.It("should not return error", func(ctx context.Context) {
				_, err := realClient.List(ctx, "/subscriptions/"+subscriptionID+"/resourceGroups/"+resourceGroupName+"notfound", nil)
				gomega.Expect(err).NotTo(gomega.HaveOccurred())
			})
		})
	}

	beforeAllFunc = func(ctx context.Context) {

	}
	afterAllFunc = func(ctx context.Context) {
	}
}
