// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package publicipprefixclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/tracing"
	armnetwork "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/network/armnetwork/v6"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/metrics"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/policy/etag"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

type Client struct {
	*armnetwork.PublicIPPrefixesClient
	subscriptionID string
	tracer         tracing.Tracer
}

func New(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (Interface, error) {
	if options == nil {
		options = utils.GetDefaultOption()
	}
	tr := options.TracingProvider.NewTracer(utils.ModuleName, utils.ModuleVersion)

	options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, utils.FuncPolicyWrapper(etag.AppendEtag))
	client, err := armnetwork.NewPublicIPPrefixesClient(subscriptionID, credential, options)
	if err != nil {
		return nil, err
	}
	return &Client{
		PublicIPPrefixesClient: client,
		subscriptionID:         subscriptionID,
		tracer:                 tr,
	}, nil
}

const GetOperationName = "PublicIPPrefixesClient.Get"

// Get gets the PublicIPPrefix
func (client *Client) Get(ctx context.Context, resourceGroupName string, publicipprefixName string, expand *string) (result *armnetwork.PublicIPPrefix, err error) {
	var ops *armnetwork.PublicIPPrefixesClientGetOptions
	if expand != nil {
		ops = &armnetwork.PublicIPPrefixesClientGetOptions{Expand: expand}
	}
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PublicIPPrefix", "get")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, GetOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := client.PublicIPPrefixesClient.Get(ctx, resourceGroupName, publicipprefixName, ops)
	if err != nil {
		return nil, err
	}
	//handle statuscode
	return &resp.PublicIPPrefix, nil
}

const CreateOrUpdateOperationName = "PublicIPPrefixesClient.Create"

// CreateOrUpdate creates or updates a PublicIPPrefix.
func (client *Client) CreateOrUpdate(ctx context.Context, resourceGroupName string, publicipprefixName string, resource armnetwork.PublicIPPrefix) (result *armnetwork.PublicIPPrefix, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PublicIPPrefix", "create_or_update")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, CreateOrUpdateOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := utils.NewPollerWrapper(client.PublicIPPrefixesClient.BeginCreateOrUpdate(ctx, resourceGroupName, publicipprefixName, resource, nil)).WaitforPollerResp(ctx)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &resp.PublicIPPrefix, nil
	}
	return nil, nil
}

const DeleteOperationName = "PublicIPPrefixesClient.Delete"

// Delete deletes a PublicIPPrefix by name.
func (client *Client) Delete(ctx context.Context, resourceGroupName string, publicipprefixName string) (err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PublicIPPrefix", "delete")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, DeleteOperationName, client.tracer, nil)
	defer endSpan(err)
	_, err = utils.NewPollerWrapper(client.BeginDelete(ctx, resourceGroupName, publicipprefixName, nil)).WaitforPollerResp(ctx)
	return err
}

const ListOperationName = "PublicIPPrefixesClient.List"

// List gets a list of PublicIPPrefix in the resource group.
func (client *Client) List(ctx context.Context, resourceGroupName string) (result []*armnetwork.PublicIPPrefix, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "PublicIPPrefix", "list")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, ListOperationName, client.tracer, nil)
	defer endSpan(err)
	pager := client.PublicIPPrefixesClient.NewListPager(resourceGroupName, nil)
	for pager.More() {
		nextResult, err := pager.NextPage(ctx)
		if err != nil {
			return nil, err
		}
		result = append(result, nextResult.Value...)
	}
	return result, nil
}
