---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 21
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "21"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-PublicIPPrefix?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 241
        uncompressed: false
        body: '{"id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix","name":"aks-cit-PublicIPPrefix","type":"Microsoft.Resources/resourceGroups","location":"eastus","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "241"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 130
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus","properties":{"prefixLength":31,"publicIPAddressVersion":"IPv4"},"sku":{"name":"Standard","tier":"Regional"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "130"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 500
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/publicIPPrefixes","location":"eastus","properties":{"provisioningState":"Updating","resourceGuid":"00000000-0000-0000-0000-000000000000","prefixLength":31,"publicIPAddressVersion":"IPv4","ipTags":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "500"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11998"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 530
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/publicIPPrefixes","location":"eastus","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","prefixLength":31,"publicIPAddressVersion":"IPv4","ipPrefix":"************/31","ipTags":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "530"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16498"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 530
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/publicIPPrefixes","location":"eastus","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","prefixLength":31,"publicIPAddressVersion":"IPv4","ipPrefix":"************/31","ipTags":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "530"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16498"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResourcenotfound?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 249
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.Network/publicIPPrefixes/testResourcenotfound'' under resource group ''aks-cit-PublicIPPrefix'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "249"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 130
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus","properties":{"prefixLength":31,"publicIPAddressVersion":"IPv4"},"sku":{"name":"Standard","tier":"Regional"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "130"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 530
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/publicIPPrefixes","location":"eastus","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","prefixLength":31,"publicIPAddressVersion":"IPv4","ipPrefix":"************/31","ipTags":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "530"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 530
        uncompressed: false
        body: '{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/publicIPPrefixes","location":"eastus","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","prefixLength":31,"publicIPAddressVersion":"IPv4","ipPrefix":"************/31","ipTags":[]},"sku":{"name":"Standard","tier":"Regional"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "530"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"00000000-0000-0000-0000-000000000000"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16496"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 147
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"etag":"invalid","location":"eastus","properties":{"prefixLength":31,"publicIPAddressVersion":"IPv4"},"sku":{"name":"Standard","tier":"Regional"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "147"
            Content-Type:
                - application/json
            If-Match:
                - invalid
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 427
        uncompressed: false
        body: '{"error":{"code":"PreconditionFailed","message":"Precondition failed.","details":[{"code":"PreconditionFailedEtagMismatch","message":"Etag provided in if-match header W/\"invalid\" does not match etag W/\"00000000-0000-0000-0000-000000000000\" of resource /subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource in NRP data store."}]}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "427"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11998"
        status: 412 Precondition Failed
        code: 412
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 542
        uncompressed: false
        body: '{"value":[{"name":"testResource","id":"/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource","etag":"W/\"00000000-0000-0000-0000-000000000000\"","type":"Microsoft.Network/publicIPPrefixes","location":"eastus","properties":{"provisioningState":"Succeeded","resourceGuid":"00000000-0000-0000-0000-000000000000","prefixLength":31,"publicIPAddressVersion":"IPv4","ipPrefix":"************/31","ipTags":[]},"sku":{"name":"Standard","tier":"Regional"}}]}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "542"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16495"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefixnotfound/providers/Microsoft.Network/publicIPPrefixes?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 122
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-PublicIPPrefixnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "122"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/aks-cit-PublicIPPrefix/providers/Microsoft.Network/publicIPPrefixes/testResource?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus/operationResults/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-PublicIPPrefix-clien azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/providers/Microsoft.Network/locations/eastus/operations/00000000-0000-0000-0000-000000000000?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 13
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/aks-cit-PublicIPPrefix?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRQVUJMSUNJUFBSRUZJWC1FQVNUVVMiLCJqb2JMb2NhdGlvbiI6ImVhc3R1cyJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 14
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/00000000-0000-0000-0000-000000000000/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRQVUJMSUNJUFBSRUZJWC1FQVNUVVMiLCJqb2JMb2NhdGlvbiI6ImVhc3R1cyJ9?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
