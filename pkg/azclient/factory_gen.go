// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package azclient

import (
	"strings"
	"sync"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/cloud"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/accountclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/availabilitysetclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/backendaddresspoolclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/blobcontainerclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/blobservicepropertiesclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/deploymentclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/diskclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/fileservicepropertiesclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/fileshareclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/identityclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/interfaceclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/ipgroupclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/loadbalancerclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/managedclusterclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/policy/ratelimit"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/privatednszonegroupclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/privateendpointclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/privatelinkserviceclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/privatezoneclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/providerclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/publicipaddressclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/publicipprefixclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/registryclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/resourcegroupclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/roleassignmentclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/routetableclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/secretclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/securitygroupclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/snapshotclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/sshpublickeyresourceclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/subnetclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/vaultclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/virtualmachineclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/virtualmachinescalesetclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/virtualmachinescalesetvmclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/virtualnetworkclient"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/virtualnetworklinkclient"
)

type ClientFactoryImpl struct {
	armConfig                               *ARMClientConfig
	cloudConfig                             cloud.Configuration
	factoryConfig                           *ClientFactoryConfig
	cred                                    azcore.TokenCredential
	clientOptionsMutFn                      []func(option *arm.ClientOptions)
	accountclientInterface                  sync.Map
	availabilitysetclientInterface          availabilitysetclient.Interface
	backendaddresspoolclientInterface       backendaddresspoolclient.Interface
	blobcontainerclientInterface            sync.Map
	blobservicepropertiesclientInterface    sync.Map
	deploymentclientInterface               deploymentclient.Interface
	diskclientInterface                     sync.Map
	fileservicepropertiesclientInterface    sync.Map
	fileshareclientInterface                sync.Map
	identityclientInterface                 identityclient.Interface
	interfaceclientInterface                interfaceclient.Interface
	ipgroupclientInterface                  ipgroupclient.Interface
	loadbalancerclientInterface             loadbalancerclient.Interface
	managedclusterclientInterface           managedclusterclient.Interface
	privatednszonegroupclientInterface      privatednszonegroupclient.Interface
	privateendpointclientInterface          privateendpointclient.Interface
	privatelinkserviceclientInterface       privatelinkserviceclient.Interface
	privatezoneclientInterface              privatezoneclient.Interface
	providerclientInterface                 providerclient.Interface
	publicipaddressclientInterface          publicipaddressclient.Interface
	publicipprefixclientInterface           publicipprefixclient.Interface
	registryclientInterface                 registryclient.Interface
	resourcegroupclientInterface            resourcegroupclient.Interface
	roleassignmentclientInterface           roleassignmentclient.Interface
	routetableclientInterface               routetableclient.Interface
	secretclientInterface                   secretclient.Interface
	securitygroupclientInterface            securitygroupclient.Interface
	snapshotclientInterface                 sync.Map
	sshpublickeyresourceclientInterface     sshpublickeyresourceclient.Interface
	subnetclientInterface                   subnetclient.Interface
	vaultclientInterface                    vaultclient.Interface
	virtualmachineclientInterface           virtualmachineclient.Interface
	virtualmachinescalesetclientInterface   virtualmachinescalesetclient.Interface
	virtualmachinescalesetvmclientInterface virtualmachinescalesetvmclient.Interface
	virtualnetworkclientInterface           virtualnetworkclient.Interface
	virtualnetworklinkclientInterface       virtualnetworklinkclient.Interface
}

func NewClientFactory(config *ClientFactoryConfig, armConfig *ARMClientConfig, cloud cloud.Configuration, cred azcore.TokenCredential, clientOptionsMutFn ...func(option *arm.ClientOptions)) (ClientFactory, error) {
	if config == nil {
		config = &ClientFactoryConfig{}
	}
	if cred == nil {
		cred = &azidentity.DefaultAzureCredential{}
	}

	var err error

	factory := &ClientFactoryImpl{
		armConfig:          armConfig,
		factoryConfig:      config,
		cloudConfig:        cloud,
		cred:               cred,
		clientOptionsMutFn: clientOptionsMutFn,
	}

	//initialize accountclient
	_, err = factory.GetAccountClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize availabilitysetclient
	factory.availabilitysetclientInterface, err = factory.createAvailabilitySetClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize backendaddresspoolclient
	factory.backendaddresspoolclientInterface, err = factory.createBackendAddressPoolClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize blobcontainerclient
	_, err = factory.GetBlobContainerClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize blobservicepropertiesclient
	_, err = factory.GetBlobServicePropertiesClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize deploymentclient
	factory.deploymentclientInterface, err = factory.createDeploymentClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize diskclient
	_, err = factory.GetDiskClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize fileservicepropertiesclient
	_, err = factory.GetFileServicePropertiesClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize fileshareclient
	_, err = factory.GetFileShareClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize identityclient
	factory.identityclientInterface, err = factory.createIdentityClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize interfaceclient
	factory.interfaceclientInterface, err = factory.createInterfaceClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize ipgroupclient
	factory.ipgroupclientInterface, err = factory.createIPGroupClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize loadbalancerclient
	factory.loadbalancerclientInterface, err = factory.createLoadBalancerClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize managedclusterclient
	factory.managedclusterclientInterface, err = factory.createManagedClusterClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize privatednszonegroupclient
	factory.privatednszonegroupclientInterface, err = factory.createPrivateDNSZoneGroupClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize privateendpointclient
	factory.privateendpointclientInterface, err = factory.createPrivateEndpointClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize privatelinkserviceclient
	factory.privatelinkserviceclientInterface, err = factory.createPrivateLinkServiceClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize privatezoneclient
	factory.privatezoneclientInterface, err = factory.createPrivateZoneClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize providerclient
	factory.providerclientInterface, err = factory.createProviderClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize publicipaddressclient
	factory.publicipaddressclientInterface, err = factory.createPublicIPAddressClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize publicipprefixclient
	factory.publicipprefixclientInterface, err = factory.createPublicIPPrefixClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize registryclient
	factory.registryclientInterface, err = factory.createRegistryClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize resourcegroupclient
	factory.resourcegroupclientInterface, err = factory.createResourceGroupClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize roleassignmentclient
	factory.roleassignmentclientInterface, err = factory.createRoleAssignmentClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize routetableclient
	factory.routetableclientInterface, err = factory.createRouteTableClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize secretclient
	factory.secretclientInterface, err = factory.createSecretClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize securitygroupclient
	factory.securitygroupclientInterface, err = factory.createSecurityGroupClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize snapshotclient
	_, err = factory.GetSnapshotClientForSub(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize sshpublickeyresourceclient
	factory.sshpublickeyresourceclientInterface, err = factory.createSSHPublicKeyResourceClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize subnetclient
	factory.subnetclientInterface, err = factory.createSubnetClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize vaultclient
	factory.vaultclientInterface, err = factory.createVaultClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize virtualmachineclient
	factory.virtualmachineclientInterface, err = factory.createVirtualMachineClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize virtualmachinescalesetclient
	factory.virtualmachinescalesetclientInterface, err = factory.createVirtualMachineScaleSetClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize virtualmachinescalesetvmclient
	factory.virtualmachinescalesetvmclientInterface, err = factory.createVirtualMachineScaleSetVMClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize virtualnetworkclient
	factory.virtualnetworkclientInterface, err = factory.createVirtualNetworkClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}

	//initialize virtualnetworklinkclient
	factory.virtualnetworklinkclientInterface, err = factory.createVirtualNetworkLinkClient(config.SubscriptionID)
	if err != nil {
		return nil, err
	}
	return factory, nil
}

func (factory *ClientFactoryImpl) createAccountClient(subscription string) (accountclient.Interface, error) {
	//initialize accountclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = accountclient.AzureStackCloudAPIVersion
	} else if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = accountclient.MooncakeApiVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("storageAccountRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return accountclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetAccountClient() accountclient.Interface {
	clientImp, _ := factory.accountclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(accountclient.Interface)
}
func (factory *ClientFactoryImpl) GetAccountClientForSub(subscriptionID string) (accountclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.accountclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(accountclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createAccountClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.accountclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(accountclient.Interface), nil
}

func (factory *ClientFactoryImpl) createAvailabilitySetClient(subscription string) (availabilitysetclient.Interface, error) {
	//initialize availabilitysetclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = availabilitysetclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("availabilitySetRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return availabilitysetclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetAvailabilitySetClient() availabilitysetclient.Interface {
	return factory.availabilitysetclientInterface
}

func (factory *ClientFactoryImpl) createBackendAddressPoolClient(subscription string) (backendaddresspoolclient.Interface, error) {
	//initialize backendaddresspoolclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("loadBalancerRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return backendaddresspoolclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetBackendAddressPoolClient() backendaddresspoolclient.Interface {
	return factory.backendaddresspoolclientInterface
}

func (factory *ClientFactoryImpl) createBlobContainerClient(subscription string) (blobcontainerclient.Interface, error) {
	//initialize blobcontainerclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = blobcontainerclient.AzureStackCloudAPIVersion
	} else if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = blobcontainerclient.MooncakeApiVersion
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return blobcontainerclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetBlobContainerClient() blobcontainerclient.Interface {
	clientImp, _ := factory.blobcontainerclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(blobcontainerclient.Interface)
}
func (factory *ClientFactoryImpl) GetBlobContainerClientForSub(subscriptionID string) (blobcontainerclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.blobcontainerclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(blobcontainerclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createBlobContainerClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.blobcontainerclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(blobcontainerclient.Interface), nil
}

func (factory *ClientFactoryImpl) createBlobServicePropertiesClient(subscription string) (blobservicepropertiesclient.Interface, error) {
	//initialize blobservicepropertiesclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = blobservicepropertiesclient.AzureStackCloudAPIVersion
	} else if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = blobservicepropertiesclient.MooncakeApiVersion
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return blobservicepropertiesclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetBlobServicePropertiesClient() blobservicepropertiesclient.Interface {
	clientImp, _ := factory.blobservicepropertiesclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(blobservicepropertiesclient.Interface)
}
func (factory *ClientFactoryImpl) GetBlobServicePropertiesClientForSub(subscriptionID string) (blobservicepropertiesclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.blobservicepropertiesclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(blobservicepropertiesclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createBlobServicePropertiesClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.blobservicepropertiesclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(blobservicepropertiesclient.Interface), nil
}

func (factory *ClientFactoryImpl) createDeploymentClient(subscription string) (deploymentclient.Interface, error) {
	//initialize deploymentclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("deploymentRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return deploymentclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetDeploymentClient() deploymentclient.Interface {
	return factory.deploymentclientInterface
}

func (factory *ClientFactoryImpl) createDiskClient(subscription string) (diskclient.Interface, error) {
	//initialize diskclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = diskclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("diskRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return diskclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetDiskClient() diskclient.Interface {
	clientImp, _ := factory.diskclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(diskclient.Interface)
}
func (factory *ClientFactoryImpl) GetDiskClientForSub(subscriptionID string) (diskclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.diskclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(diskclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createDiskClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.diskclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(diskclient.Interface), nil
}

func (factory *ClientFactoryImpl) createFileServicePropertiesClient(subscription string) (fileservicepropertiesclient.Interface, error) {
	//initialize fileservicepropertiesclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = fileservicepropertiesclient.MooncakeApiVersion
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return fileservicepropertiesclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetFileServicePropertiesClient() fileservicepropertiesclient.Interface {
	clientImp, _ := factory.fileservicepropertiesclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(fileservicepropertiesclient.Interface)
}
func (factory *ClientFactoryImpl) GetFileServicePropertiesClientForSub(subscriptionID string) (fileservicepropertiesclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.fileservicepropertiesclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(fileservicepropertiesclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createFileServicePropertiesClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.fileservicepropertiesclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(fileservicepropertiesclient.Interface), nil
}

func (factory *ClientFactoryImpl) createFileShareClient(subscription string) (fileshareclient.Interface, error) {
	//initialize fileshareclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = fileshareclient.MooncakeApiVersion
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return fileshareclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetFileShareClient() fileshareclient.Interface {
	clientImp, _ := factory.fileshareclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(fileshareclient.Interface)
}
func (factory *ClientFactoryImpl) GetFileShareClientForSub(subscriptionID string) (fileshareclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.fileshareclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(fileshareclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createFileShareClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.fileshareclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(fileshareclient.Interface), nil
}

func (factory *ClientFactoryImpl) createIdentityClient(subscription string) (identityclient.Interface, error) {
	//initialize identityclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return identityclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetIdentityClient() identityclient.Interface {
	return factory.identityclientInterface
}

func (factory *ClientFactoryImpl) createInterfaceClient(subscription string) (interfaceclient.Interface, error) {
	//initialize interfaceclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = interfaceclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("interfaceRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return interfaceclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetInterfaceClient() interfaceclient.Interface {
	return factory.interfaceclientInterface
}

func (factory *ClientFactoryImpl) createIPGroupClient(subscription string) (ipgroupclient.Interface, error) {
	//initialize ipgroupclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = ipgroupclient.MooncakeApiVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("ipGroupRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return ipgroupclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetIPGroupClient() ipgroupclient.Interface {
	return factory.ipgroupclientInterface
}

func (factory *ClientFactoryImpl) createLoadBalancerClient(subscription string) (loadbalancerclient.Interface, error) {
	//initialize loadbalancerclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = loadbalancerclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("loadBalancerRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return loadbalancerclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetLoadBalancerClient() loadbalancerclient.Interface {
	return factory.loadbalancerclientInterface
}

func (factory *ClientFactoryImpl) createManagedClusterClient(subscription string) (managedclusterclient.Interface, error) {
	//initialize managedclusterclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("containerServiceRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return managedclusterclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetManagedClusterClient() managedclusterclient.Interface {
	return factory.managedclusterclientInterface
}

func (factory *ClientFactoryImpl) createPrivateDNSZoneGroupClient(subscription string) (privatednszonegroupclient.Interface, error) {
	//initialize privatednszonegroupclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return privatednszonegroupclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetPrivateDNSZoneGroupClient() privatednszonegroupclient.Interface {
	return factory.privatednszonegroupclientInterface
}

func (factory *ClientFactoryImpl) createPrivateEndpointClient(subscription string) (privateendpointclient.Interface, error) {
	//initialize privateendpointclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("privateEndpointRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return privateendpointclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetPrivateEndpointClient() privateendpointclient.Interface {
	return factory.privateendpointclientInterface
}

func (factory *ClientFactoryImpl) createPrivateLinkServiceClient(subscription string) (privatelinkserviceclient.Interface, error) {
	//initialize privatelinkserviceclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = privatelinkserviceclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("privateLinkServiceRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return privatelinkserviceclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetPrivateLinkServiceClient() privatelinkserviceclient.Interface {
	return factory.privatelinkserviceclientInterface
}

func (factory *ClientFactoryImpl) createPrivateZoneClient(subscription string) (privatezoneclient.Interface, error) {
	//initialize privatezoneclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = privatezoneclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("privateDNSRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return privatezoneclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetPrivateZoneClient() privatezoneclient.Interface {
	return factory.privatezoneclientInterface
}

func (factory *ClientFactoryImpl) createProviderClient(subscription string) (providerclient.Interface, error) {
	//initialize providerclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return providerclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetProviderClient() providerclient.Interface {
	return factory.providerclientInterface
}

func (factory *ClientFactoryImpl) createPublicIPAddressClient(subscription string) (publicipaddressclient.Interface, error) {
	//initialize publicipaddressclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = publicipaddressclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("publicIPAddressRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return publicipaddressclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetPublicIPAddressClient() publicipaddressclient.Interface {
	return factory.publicipaddressclientInterface
}

func (factory *ClientFactoryImpl) createPublicIPPrefixClient(subscription string) (publicipprefixclient.Interface, error) {
	//initialize publicipprefixclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return publicipprefixclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetPublicIPPrefixClient() publicipprefixclient.Interface {
	return factory.publicipprefixclientInterface
}

func (factory *ClientFactoryImpl) createRegistryClient(subscription string) (registryclient.Interface, error) {
	//initialize registryclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if !strings.EqualFold(options.Cloud.ActiveDirectoryAuthorityHost, cloud.AzurePublic.ActiveDirectoryAuthorityHost) {
		options.ClientOptions.APIVersion = registryclient.MooncakeApiVersion
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return registryclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetRegistryClient() registryclient.Interface {
	return factory.registryclientInterface
}

func (factory *ClientFactoryImpl) createResourceGroupClient(subscription string) (resourcegroupclient.Interface, error) {
	//initialize resourcegroupclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return resourcegroupclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetResourceGroupClient() resourcegroupclient.Interface {
	return factory.resourcegroupclientInterface
}

func (factory *ClientFactoryImpl) createRoleAssignmentClient(subscription string) (roleassignmentclient.Interface, error) {
	//initialize roleassignmentclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return roleassignmentclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetRoleAssignmentClient() roleassignmentclient.Interface {
	return factory.roleassignmentclientInterface
}

func (factory *ClientFactoryImpl) createRouteTableClient(subscription string) (routetableclient.Interface, error) {
	//initialize routetableclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = routetableclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("routeTableRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return routetableclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetRouteTableClient() routetableclient.Interface {
	return factory.routetableclientInterface
}

func (factory *ClientFactoryImpl) createSecretClient(subscription string) (secretclient.Interface, error) {
	//initialize secretclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return secretclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetSecretClient() secretclient.Interface {
	return factory.secretclientInterface
}

func (factory *ClientFactoryImpl) createSecurityGroupClient(subscription string) (securitygroupclient.Interface, error) {
	//initialize securitygroupclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = securitygroupclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("securityGroupRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return securitygroupclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetSecurityGroupClient() securitygroupclient.Interface {
	return factory.securitygroupclientInterface
}

func (factory *ClientFactoryImpl) createSnapshotClient(subscription string) (snapshotclient.Interface, error) {
	//initialize snapshotclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = snapshotclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("snapshotRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return snapshotclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetSnapshotClient() snapshotclient.Interface {
	clientImp, _ := factory.snapshotclientInterface.Load(strings.ToLower(factory.factoryConfig.SubscriptionID))
	return clientImp.(snapshotclient.Interface)
}
func (factory *ClientFactoryImpl) GetSnapshotClientForSub(subscriptionID string) (snapshotclient.Interface, error) {
	if subscriptionID == "" {
		subscriptionID = factory.factoryConfig.SubscriptionID
	}
	clientImp, loaded := factory.snapshotclientInterface.Load(strings.ToLower(subscriptionID))
	if loaded {
		return clientImp.(snapshotclient.Interface), nil
	}
	//It's not thread safe, but it's ok for now. because it will be called once.
	clientImp, err := factory.createSnapshotClient(subscriptionID)
	if err != nil {
		return nil, err
	}
	factory.snapshotclientInterface.Store(strings.ToLower(subscriptionID), clientImp)
	return clientImp.(snapshotclient.Interface), nil
}

func (factory *ClientFactoryImpl) createSSHPublicKeyResourceClient(subscription string) (sshpublickeyresourceclient.Interface, error) {
	//initialize sshpublickeyresourceclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return sshpublickeyresourceclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetSSHPublicKeyResourceClient() sshpublickeyresourceclient.Interface {
	return factory.sshpublickeyresourceclientInterface
}

func (factory *ClientFactoryImpl) createSubnetClient(subscription string) (subnetclient.Interface, error) {
	//initialize subnetclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = subnetclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("subnetsRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return subnetclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetSubnetClient() subnetclient.Interface {
	return factory.subnetclientInterface
}

func (factory *ClientFactoryImpl) createVaultClient(subscription string) (vaultclient.Interface, error) {
	//initialize vaultclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return vaultclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetVaultClient() vaultclient.Interface {
	return factory.vaultclientInterface
}

func (factory *ClientFactoryImpl) createVirtualMachineClient(subscription string) (virtualmachineclient.Interface, error) {
	//initialize virtualmachineclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = virtualmachineclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("virtualMachineRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return virtualmachineclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetVirtualMachineClient() virtualmachineclient.Interface {
	return factory.virtualmachineclientInterface
}

func (factory *ClientFactoryImpl) createVirtualMachineScaleSetClient(subscription string) (virtualmachinescalesetclient.Interface, error) {
	//initialize virtualmachinescalesetclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = virtualmachinescalesetclient.AzureStackCloudAPIVersion
	}
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("virtualMachineScaleSetRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return virtualmachinescalesetclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetVirtualMachineScaleSetClient() virtualmachinescalesetclient.Interface {
	return factory.virtualmachinescalesetclientInterface
}

func (factory *ClientFactoryImpl) createVirtualMachineScaleSetVMClient(subscription string) (virtualmachinescalesetvmclient.Interface, error) {
	//initialize virtualmachinescalesetvmclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	if factory.armConfig != nil && strings.EqualFold(factory.armConfig.Cloud, utils.AzureStackCloudName) && !factory.armConfig.DisableAzureStackCloud {
		options.ClientOptions.APIVersion = virtualmachinescalesetvmclient.AzureStackCloudAPIVersion
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return virtualmachinescalesetvmclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetVirtualMachineScaleSetVMClient() virtualmachinescalesetvmclient.Interface {
	return factory.virtualmachinescalesetvmclientInterface
}

func (factory *ClientFactoryImpl) createVirtualNetworkClient(subscription string) (virtualnetworkclient.Interface, error) {
	//initialize virtualnetworkclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return virtualnetworkclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetVirtualNetworkClient() virtualnetworkclient.Interface {
	return factory.virtualnetworkclientInterface
}

func (factory *ClientFactoryImpl) createVirtualNetworkLinkClient(subscription string) (virtualnetworklinkclient.Interface, error) {
	//initialize virtualnetworklinkclient
	options, err := GetDefaultResourceClientOption(factory.armConfig)
	if err != nil {
		return nil, err
	}
	options.Cloud = factory.cloudConfig
	//add ratelimit policy
	ratelimitOption := factory.factoryConfig.GetRateLimitConfig("virtualNetworkRateLimit")
	rateLimitPolicy := ratelimit.NewRateLimitPolicy(ratelimitOption)
	if rateLimitPolicy != nil {
		options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, rateLimitPolicy)
	}
	for _, optionMutFn := range factory.clientOptionsMutFn {
		if optionMutFn != nil {
			optionMutFn(options)
		}
	}
	return virtualnetworklinkclient.New(subscription, factory.cred, options)
}

func (factory *ClientFactoryImpl) GetVirtualNetworkLinkClient() virtualnetworklinkclient.Interface {
	return factory.virtualnetworklinkclientInterface
}
