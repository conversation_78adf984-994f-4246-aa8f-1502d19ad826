{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "string", "allowedValues": ["China East", "China North"], "metadata": {"description": "Location to deploy to"}}}, "resources": [{"type": "Microsoft.Compute/availabilitySets", "name": "availabilitySet1", "apiVersion": "2019-07-01", "location": "[parameters('location')]", "properties": {}}], "outputs": {"myparameter": {"type": "object", "value": "[reference('Microsoft.Compute/availabilitySets/availabilitySet1')]"}}}