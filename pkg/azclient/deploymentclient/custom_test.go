// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package deploymentclient

import (
	"context"
	"encoding/json"
	"os"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armresources "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

var newResource *armresources.Deployment

func init() {
	additionalTestCases = func() {
		When("creation requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				newResource, err := realClient.CreateOrUpdate(ctx, resourceGroupName, resourceName, *newResource)
				Expect(err).NotTo(HaveOccurred())
				Expect(newResource).NotTo(BeNil())
				Expect(*newResource.Name).To(Equal(resourceName))
			})
		})
		When("get requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				newResource, err := realClient.Get(ctx, resourceGroupName, resourceName)
				Expect(err).NotTo(HaveOccurred())
				Expect(newResource).NotTo(BeNil())
			})
		})
		When("invalid get requests are raised", func() {
			It("should return 404 error", func(ctx context.Context) {
				newResource, err := realClient.Get(ctx, resourceGroupName, resourceName+"notfound")
				Expect(err).To(HaveOccurred())
				Expect(newResource).To(BeNil())
			})
		})

		When("list requests are raised", func() {
			It("should not return error", func(ctx context.Context) {
				resourceList, err := realClient.List(ctx, resourceGroupName)
				Expect(err).NotTo(HaveOccurred())
				Expect(resourceList).NotTo(BeNil())
				Expect(len(resourceList)).To(Equal(1))
				Expect(*resourceList[0].Name).To(Equal(resourceName))
			})
		})
		When("invalid list requests are raised", func() {
			It("should return error", func(ctx context.Context) {
				resourceList, err := realClient.List(ctx, resourceGroupName+"notfound")
				Expect(err).To(HaveOccurred())
				Expect(resourceList).To(BeNil())
			})
		})
	}

	beforeAllFunc = func(ctx context.Context) {
		testTemplate := "testdata/template.json"
		testParams := "testdata/parameter.json"
		if location == "chinaeast2" {
			testTemplate = "testdata/template_chinacloud.json"
			testParams = "testdata/parameter_chinacloud.json"
		}
		template, err := readJson(testTemplate)
		Expect(err).NotTo(HaveOccurred())
		params, err := readJson(testParams)
		Expect(err).NotTo(HaveOccurred())
		newResource = &armresources.Deployment{
			Properties: &armresources.DeploymentProperties{
				Template:   template,
				Parameters: params,
				Mode:       to.Ptr(armresources.DeploymentModeIncremental),
			},
		}
	}
	afterAllFunc = func(ctx context.Context) {
	}
}

func readJson(path string) (map[string]interface{}, error) {
	templateFile, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	template := make(map[string]interface{})
	if err := json.Unmarshal(templateFile, &template); err != nil {
		return nil, err
	}

	return template, nil
}
