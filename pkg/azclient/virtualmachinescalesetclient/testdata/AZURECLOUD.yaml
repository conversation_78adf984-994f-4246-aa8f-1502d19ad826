---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 21
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus"}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "21"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourcegroups/aks-cit-VirtualMachineScaleSet?api-version=2021-04-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 257
        uncompressed: false
        body: '{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet","name":"aks-cit-VirtualMachineScaleSet","type":"Microsoft.Resources/resourceGroups","location":"eastus","properties":{"provisioningState":"Succeeded"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "257"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 1
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 163
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus","properties":{"addressSpace":{"addressPrefixes":["********/16"]},"subnets":[{"name":"subnet1","properties":{"addressPrefix":"********/24"}}]}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "163"
            Content-Type:
                - application/json
            User-Agent:
                - azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1023
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"********-0000-0000-0000-************\"","type":"Microsoft.Network/virtualNetworks","location":"eastus","properties":{"provisioningState":"Updating","resourceGuid":"********-0000-0000-0000-************","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"********-0000-0000-0000-************\"","properties":{"provisioningState":"Updating","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Enabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Network/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "1023"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 2
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Network/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 3
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 1025
        uncompressed: false
        body: '{"name":"vnet1","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1","etag":"W/\"********-0000-0000-0000-************\"","type":"Microsoft.Network/virtualNetworks","location":"eastus","properties":{"provisioningState":"Succeeded","resourceGuid":"********-0000-0000-0000-************","addressSpace":{"addressPrefixes":["********/16"]},"privateEndpointVNetPolicies":"Disabled","subnets":[{"name":"subnet1","id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1","etag":"W/\"********-0000-0000-0000-************\"","properties":{"provisioningState":"Succeeded","addressPrefix":"********/24","delegations":[],"privateEndpointNetworkPolicies":"Disabled","privateLinkServiceNetworkPolicies":"Enabled"},"type":"Microsoft.Network/virtualNetworks/subnets"}],"virtualNetworkPeerings":[],"enableDdosProtection":false}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "1025"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - W/"********-0000-0000-0000-************"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 4
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 256
        uncompressed: false
        body: '{"error":{"code":"ResourceNotFound","message":"The Resource ''Microsoft.Compute/virtualMachineScaleSets/testResource'' under resource group ''aks-cit-VirtualMachineScaleSet'' was not found. For more details please go to https://aka.ms/ARMResourceNotFoundFix"}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "256"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 5
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 881
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus","properties":{"overprovision":false,"upgradePolicy":{"automaticOSUpgradePolicy":{"disableAutomaticRollback":false,"enableAutomaticOSUpgrade":false},"mode":"Manual"},"virtualMachineProfile":{"networkProfile":{"networkInterfaceConfigurations":[{"name":"testResource","properties":{"enableIPForwarding":true,"ipConfigurations":[{"name":"testResource","properties":{"subnet":{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"}}}],"primary":true}}]},"osProfile":{"adminPassword":"{PASSWORD}","adminUsername":"sample-user","computerNamePrefix":"vmss"},"storageProfile":{"imageReference":{"offer":"WindowsServer","publisher":"MicrosoftWindowsServer","sku":"2019-Datacenter","version":"latest"}}}},"sku":{"capacity":1,"name":"Standard_D2s_v3"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "881"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 3545
        uncompressed: false
        body: "{\r\n  \"name\": \"testResource\",\r\n  \"id\": \"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource\",\r\n  \"type\": \"Microsoft.Compute/virtualMachineScaleSets\",\r\n  \"location\": \"eastus\",\r\n  \"tags\": {\r\n    \"azsecpack\": \"nonprod\",\r\n    \"platformsettings.host_environment.service.platform_optedin_for_rootcerts\": \"true\"\r\n  },\r\n  \"sku\": {\r\n    \"name\": \"Standard_D2s_v3\",\r\n    \"tier\": \"Standard\",\r\n    \"capacity\": 1\r\n  },\r\n  \"etag\": \"\\\"1\\\"\",\r\n  \"properties\": {\r\n    \"singlePlacementGroup\": true,\r\n    \"orchestrationMode\": \"Uniform\",\r\n    \"upgradePolicy\": {\r\n      \"mode\": \"Manual\",\r\n      \"automaticOSUpgradePolicy\": {\r\n        \"enableAutomaticOSUpgrade\": false,\r\n        \"useRollingUpgradePolicy\": false,\r\n        \"disableAutomaticRollback\": false\r\n      }\r\n    },\r\n    \"virtualMachineProfile\": {\r\n      \"osProfile\": {\r\n        \"computerNamePrefix\": \"vmss\",\r\n        \"adminUsername\": \"sample-user\",\r\n        \"windowsConfiguration\": {\r\n          \"provisionVMAgent\": true,\r\n          \"enableAutomaticUpdates\": true,\r\n          \"enableVMAgentPlatformUpdates\": true\r\n        },\r\n        \"secrets\": [],\r\n        \"allowExtensionOperations\": true,\r\n        \"requireGuestProvisionSignal\": true\r\n      },\r\n      \"storageProfile\": {\r\n        \"osDisk\": {\r\n          \"osType\": \"Windows\",\r\n          \"createOption\": \"FromImage\",\r\n          \"caching\": \"None\",\r\n          \"managedDisk\": {\r\n            \"storageAccountType\": \"Premium_LRS\"\r\n          },\r\n          \"diskSizeGB\": 127\r\n        },\r\n        \"imageReference\": {\r\n          \"publisher\": \"MicrosoftWindowsServer\",\r\n          \"offer\": \"WindowsServer\",\r\n          \"sku\": \"2019-Datacenter\",\r\n          \"version\": \"latest\"\r\n        }\r\n      },\r\n      \"networkProfile\": {\"networkInterfaceConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"primary\":true,\"enableAcceleratedNetworking\":false,\"disableTcpStateTracking\":false,\"dnsSettings\":{\"dnsServers\":[]},\"enableIPForwarding\":true,\"ipConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"subnet\":{\"id\":\"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1\"},\"privateIPAddressVersion\":\"IPv4\"}}]}}]},\r\n      \"extensionProfile\": {\r\n        \"extensions\": [\r\n          {\r\n            \"name\": \"Microsoft.Azure.Security.AntimalwareSignature.AntimalwareConfiguration\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Security.AntimalwareSignature\",\r\n              \"type\": \"AntimalwareConfiguration\",\r\n              \"typeHandlerVersion\": \"2.0\",\r\n              \"settings\": {}\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"Microsoft.Azure.Geneva.GenevaMonitoring\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Geneva\",\r\n              \"type\": \"GenevaMonitoring\",\r\n              \"typeHandlerVersion\": \"2.0\"\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n    },\r\n    \"provisioningState\": \"Creating\",\r\n    \"overprovision\": false,\r\n    \"doNotRunExtensionsOnOverprovisionedVMs\": false,\r\n    \"uniqueId\": \"********-0000-0000-0000-************\",\r\n    \"platformFaultDomainCount\": 5,\r\n    \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n  }\r\n}"
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&p=********-0000-0000-0000-************&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "3545"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"1"'
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "11999"
            X-Ms-Request-Charge:
                - "1"
        status: 201 Created
        code: 201
        duration: 200ms
    - id: 6
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&p=********-0000-0000-0000-************&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 184
        uncompressed: false
        body: "{\r\n  \"startTime\": \"2001-02-03T04:05:06Z\",\r\n  \"endTime\": \"2001-02-03T04:05:06Z\",\r\n  \"status\": \"Succeeded\",\r\n  \"name\": \"********-0000-0000-0000-************\"\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "184"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 7
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 3546
        uncompressed: false
        body: "{\r\n  \"name\": \"testResource\",\r\n  \"id\": \"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource\",\r\n  \"type\": \"Microsoft.Compute/virtualMachineScaleSets\",\r\n  \"location\": \"eastus\",\r\n  \"tags\": {\r\n    \"azsecpack\": \"nonprod\",\r\n    \"platformsettings.host_environment.service.platform_optedin_for_rootcerts\": \"true\"\r\n  },\r\n  \"sku\": {\r\n    \"name\": \"Standard_D2s_v3\",\r\n    \"tier\": \"Standard\",\r\n    \"capacity\": 1\r\n  },\r\n  \"etag\": \"\\\"1\\\"\",\r\n  \"properties\": {\r\n    \"singlePlacementGroup\": true,\r\n    \"orchestrationMode\": \"Uniform\",\r\n    \"upgradePolicy\": {\r\n      \"mode\": \"Manual\",\r\n      \"automaticOSUpgradePolicy\": {\r\n        \"enableAutomaticOSUpgrade\": false,\r\n        \"useRollingUpgradePolicy\": false,\r\n        \"disableAutomaticRollback\": false\r\n      }\r\n    },\r\n    \"virtualMachineProfile\": {\r\n      \"osProfile\": {\r\n        \"computerNamePrefix\": \"vmss\",\r\n        \"adminUsername\": \"sample-user\",\r\n        \"windowsConfiguration\": {\r\n          \"provisionVMAgent\": true,\r\n          \"enableAutomaticUpdates\": true,\r\n          \"enableVMAgentPlatformUpdates\": true\r\n        },\r\n        \"secrets\": [],\r\n        \"allowExtensionOperations\": true,\r\n        \"requireGuestProvisionSignal\": true\r\n      },\r\n      \"storageProfile\": {\r\n        \"osDisk\": {\r\n          \"osType\": \"Windows\",\r\n          \"createOption\": \"FromImage\",\r\n          \"caching\": \"None\",\r\n          \"managedDisk\": {\r\n            \"storageAccountType\": \"Premium_LRS\"\r\n          },\r\n          \"diskSizeGB\": 127\r\n        },\r\n        \"imageReference\": {\r\n          \"publisher\": \"MicrosoftWindowsServer\",\r\n          \"offer\": \"WindowsServer\",\r\n          \"sku\": \"2019-Datacenter\",\r\n          \"version\": \"latest\"\r\n        }\r\n      },\r\n      \"networkProfile\": {\"networkInterfaceConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"primary\":true,\"enableAcceleratedNetworking\":false,\"disableTcpStateTracking\":false,\"dnsSettings\":{\"dnsServers\":[]},\"enableIPForwarding\":true,\"ipConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"subnet\":{\"id\":\"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1\"},\"privateIPAddressVersion\":\"IPv4\"}}]}}]},\r\n      \"extensionProfile\": {\r\n        \"extensions\": [\r\n          {\r\n            \"name\": \"Microsoft.Azure.Security.AntimalwareSignature.AntimalwareConfiguration\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Security.AntimalwareSignature\",\r\n              \"type\": \"AntimalwareConfiguration\",\r\n              \"typeHandlerVersion\": \"2.0\",\r\n              \"settings\": {}\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"Microsoft.Azure.Geneva.GenevaMonitoring\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Geneva\",\r\n              \"type\": \"GenevaMonitoring\",\r\n              \"typeHandlerVersion\": \"2.0\"\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n    },\r\n    \"provisioningState\": \"Succeeded\",\r\n    \"overprovision\": false,\r\n    \"doNotRunExtensionsOnOverprovisionedVMs\": false,\r\n    \"uniqueId\": \"********-0000-0000-0000-************\",\r\n    \"platformFaultDomainCount\": 5,\r\n    \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n  }\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "3546"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"1"'
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 8
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 881
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"location":"eastus","properties":{"overprovision":false,"upgradePolicy":{"automaticOSUpgradePolicy":{"disableAutomaticRollback":false,"enableAutomaticOSUpgrade":false},"mode":"Manual"},"virtualMachineProfile":{"networkProfile":{"networkInterfaceConfigurations":[{"name":"testResource","properties":{"enableIPForwarding":true,"ipConfigurations":[{"name":"testResource","properties":{"subnet":{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"}}}],"primary":true}}]},"osProfile":{"adminPassword":"{PASSWORD}","adminUsername":"sample-user","computerNamePrefix":"vmss"},"storageProfile":{"imageReference":{"offer":"WindowsServer","publisher":"MicrosoftWindowsServer","sku":"2019-Datacenter","version":"latest"}}}},"sku":{"capacity":1,"name":"Standard_D2s_v3"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "881"
            Content-Type:
                - application/json
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 3546
        uncompressed: false
        body: "{\r\n  \"name\": \"testResource\",\r\n  \"id\": \"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource\",\r\n  \"type\": \"Microsoft.Compute/virtualMachineScaleSets\",\r\n  \"location\": \"eastus\",\r\n  \"tags\": {\r\n    \"azsecpack\": \"nonprod\",\r\n    \"platformsettings.host_environment.service.platform_optedin_for_rootcerts\": \"true\"\r\n  },\r\n  \"sku\": {\r\n    \"name\": \"Standard_D2s_v3\",\r\n    \"tier\": \"Standard\",\r\n    \"capacity\": 1\r\n  },\r\n  \"etag\": \"\\\"2\\\"\",\r\n  \"properties\": {\r\n    \"singlePlacementGroup\": true,\r\n    \"orchestrationMode\": \"Uniform\",\r\n    \"upgradePolicy\": {\r\n      \"mode\": \"Manual\",\r\n      \"automaticOSUpgradePolicy\": {\r\n        \"enableAutomaticOSUpgrade\": false,\r\n        \"useRollingUpgradePolicy\": false,\r\n        \"disableAutomaticRollback\": false\r\n      }\r\n    },\r\n    \"virtualMachineProfile\": {\r\n      \"osProfile\": {\r\n        \"computerNamePrefix\": \"vmss\",\r\n        \"adminUsername\": \"sample-user\",\r\n        \"windowsConfiguration\": {\r\n          \"provisionVMAgent\": true,\r\n          \"enableAutomaticUpdates\": true,\r\n          \"enableVMAgentPlatformUpdates\": false\r\n        },\r\n        \"secrets\": [],\r\n        \"allowExtensionOperations\": true,\r\n        \"requireGuestProvisionSignal\": true\r\n      },\r\n      \"storageProfile\": {\r\n        \"osDisk\": {\r\n          \"osType\": \"Windows\",\r\n          \"createOption\": \"FromImage\",\r\n          \"caching\": \"None\",\r\n          \"managedDisk\": {\r\n            \"storageAccountType\": \"Premium_LRS\"\r\n          },\r\n          \"diskSizeGB\": 127\r\n        },\r\n        \"imageReference\": {\r\n          \"publisher\": \"MicrosoftWindowsServer\",\r\n          \"offer\": \"WindowsServer\",\r\n          \"sku\": \"2019-Datacenter\",\r\n          \"version\": \"latest\"\r\n        }\r\n      },\r\n      \"networkProfile\": {\"networkInterfaceConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"primary\":true,\"enableAcceleratedNetworking\":false,\"disableTcpStateTracking\":false,\"dnsSettings\":{\"dnsServers\":[]},\"enableIPForwarding\":true,\"ipConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"subnet\":{\"id\":\"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1\"},\"privateIPAddressVersion\":\"IPv4\"}}]}}]},\r\n      \"extensionProfile\": {\r\n        \"extensions\": [\r\n          {\r\n            \"name\": \"Microsoft.Azure.Security.AntimalwareSignature.AntimalwareConfiguration\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Security.AntimalwareSignature\",\r\n              \"type\": \"AntimalwareConfiguration\",\r\n              \"typeHandlerVersion\": \"2.0\",\r\n              \"settings\": {}\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"Microsoft.Azure.Geneva.GenevaMonitoring\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Geneva\",\r\n              \"type\": \"GenevaMonitoring\",\r\n              \"typeHandlerVersion\": \"2.0\"\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n    },\r\n    \"provisioningState\": \"Updating\",\r\n    \"overprovision\": false,\r\n    \"doNotRunExtensionsOnOverprovisionedVMs\": false,\r\n    \"uniqueId\": \"********-0000-0000-0000-************\",\r\n    \"platformFaultDomainCount\": 5,\r\n    \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n  }\r\n}"
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&p=********-0000-0000-0000-************&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "3546"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"2"'
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "12000"
            X-Ms-Request-Charge:
                - "0"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 9
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&p=********-0000-0000-0000-************&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 184
        uncompressed: false
        body: "{\r\n  \"startTime\": \"2001-02-03T04:05:06Z\",\r\n  \"endTime\": \"2001-02-03T04:05:06Z\",\r\n  \"status\": \"Succeeded\",\r\n  \"name\": \"********-0000-0000-0000-************\"\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "184"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 10
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 3547
        uncompressed: false
        body: "{\r\n  \"name\": \"testResource\",\r\n  \"id\": \"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource\",\r\n  \"type\": \"Microsoft.Compute/virtualMachineScaleSets\",\r\n  \"location\": \"eastus\",\r\n  \"tags\": {\r\n    \"azsecpack\": \"nonprod\",\r\n    \"platformsettings.host_environment.service.platform_optedin_for_rootcerts\": \"true\"\r\n  },\r\n  \"sku\": {\r\n    \"name\": \"Standard_D2s_v3\",\r\n    \"tier\": \"Standard\",\r\n    \"capacity\": 1\r\n  },\r\n  \"etag\": \"\\\"2\\\"\",\r\n  \"properties\": {\r\n    \"singlePlacementGroup\": true,\r\n    \"orchestrationMode\": \"Uniform\",\r\n    \"upgradePolicy\": {\r\n      \"mode\": \"Manual\",\r\n      \"automaticOSUpgradePolicy\": {\r\n        \"enableAutomaticOSUpgrade\": false,\r\n        \"useRollingUpgradePolicy\": false,\r\n        \"disableAutomaticRollback\": false\r\n      }\r\n    },\r\n    \"virtualMachineProfile\": {\r\n      \"osProfile\": {\r\n        \"computerNamePrefix\": \"vmss\",\r\n        \"adminUsername\": \"sample-user\",\r\n        \"windowsConfiguration\": {\r\n          \"provisionVMAgent\": true,\r\n          \"enableAutomaticUpdates\": true,\r\n          \"enableVMAgentPlatformUpdates\": false\r\n        },\r\n        \"secrets\": [],\r\n        \"allowExtensionOperations\": true,\r\n        \"requireGuestProvisionSignal\": true\r\n      },\r\n      \"storageProfile\": {\r\n        \"osDisk\": {\r\n          \"osType\": \"Windows\",\r\n          \"createOption\": \"FromImage\",\r\n          \"caching\": \"None\",\r\n          \"managedDisk\": {\r\n            \"storageAccountType\": \"Premium_LRS\"\r\n          },\r\n          \"diskSizeGB\": 127\r\n        },\r\n        \"imageReference\": {\r\n          \"publisher\": \"MicrosoftWindowsServer\",\r\n          \"offer\": \"WindowsServer\",\r\n          \"sku\": \"2019-Datacenter\",\r\n          \"version\": \"latest\"\r\n        }\r\n      },\r\n      \"networkProfile\": {\"networkInterfaceConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"primary\":true,\"enableAcceleratedNetworking\":false,\"disableTcpStateTracking\":false,\"dnsSettings\":{\"dnsServers\":[]},\"enableIPForwarding\":true,\"ipConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"subnet\":{\"id\":\"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1\"},\"privateIPAddressVersion\":\"IPv4\"}}]}}]},\r\n      \"extensionProfile\": {\r\n        \"extensions\": [\r\n          {\r\n            \"name\": \"Microsoft.Azure.Security.AntimalwareSignature.AntimalwareConfiguration\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Security.AntimalwareSignature\",\r\n              \"type\": \"AntimalwareConfiguration\",\r\n              \"typeHandlerVersion\": \"2.0\",\r\n              \"settings\": {}\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"Microsoft.Azure.Geneva.GenevaMonitoring\",\r\n            \"properties\": {\r\n              \"autoUpgradeMinorVersion\": true,\r\n              \"enableAutomaticUpgrade\": true,\r\n              \"publisher\": \"Microsoft.Azure.Geneva\",\r\n              \"type\": \"GenevaMonitoring\",\r\n              \"typeHandlerVersion\": \"2.0\"\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n    },\r\n    \"provisioningState\": \"Succeeded\",\r\n    \"overprovision\": false,\r\n    \"doNotRunExtensionsOnOverprovisionedVMs\": false,\r\n    \"uniqueId\": \"********-0000-0000-0000-************\",\r\n    \"platformFaultDomainCount\": 5,\r\n    \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n  }\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "3547"
            Content-Type:
                - application/json; charset=utf-8
            Etag:
                - '"2"'
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 11
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 898
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: '{"etag":"invalid","location":"eastus","properties":{"overprovision":false,"upgradePolicy":{"automaticOSUpgradePolicy":{"disableAutomaticRollback":false,"enableAutomaticOSUpgrade":false},"mode":"Manual"},"virtualMachineProfile":{"networkProfile":{"networkInterfaceConfigurations":[{"name":"testResource","properties":{"enableIPForwarding":true,"ipConfigurations":[{"name":"testResource","properties":{"subnet":{"id":"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1"}}}],"primary":true}}]},"osProfile":{"adminPassword":"{PASSWORD}","adminUsername":"sample-user","computerNamePrefix":"vmss"},"storageProfile":{"imageReference":{"offer":"WindowsServer","publisher":"MicrosoftWindowsServer","sku":"2019-Datacenter","version":"latest"}}}},"sku":{"capacity":1,"name":"Standard_D2s_v3"}}'
        form: {}
        headers:
            Accept:
                - application/json
            Content-Length:
                - "898"
            Content-Type:
                - application/json
            If-Match:
                - invalid
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: PUT
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 160
        uncompressed: false
        body: "{\r\n  \"error\": {\r\n    \"code\": \"PreconditionFailed\",\r\n    \"message\": \"Etag provided in if-match header \\\"invalid\\\" does not match etag \\\"2\\\" of resource.\"\r\n  }\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "160"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Writes:
                - "12000"
        status: 412 Precondition Failed
        code: 412
        duration: 200ms
    - id: 12
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets?api-version=2024-07-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 3940
        uncompressed: false
        body: "{\r\n  \"value\": [\r\n    {\r\n      \"name\": \"testResource\",\r\n      \"id\": \"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource\",\r\n      \"type\": \"Microsoft.Compute/virtualMachineScaleSets\",\r\n      \"location\": \"eastus\",\r\n      \"tags\": {\r\n        \"azsecpack\": \"nonprod\",\r\n        \"platformsettings.host_environment.service.platform_optedin_for_rootcerts\": \"true\"\r\n      },\r\n      \"sku\": {\r\n        \"name\": \"Standard_D2s_v3\",\r\n        \"tier\": \"Standard\",\r\n        \"capacity\": 1\r\n      },\r\n      \"etag\": \"\\\"2\\\"\",\r\n      \"properties\": {\r\n        \"singlePlacementGroup\": true,\r\n        \"orchestrationMode\": \"Uniform\",\r\n        \"upgradePolicy\": {\r\n          \"mode\": \"Manual\",\r\n          \"automaticOSUpgradePolicy\": {\r\n            \"enableAutomaticOSUpgrade\": false,\r\n            \"useRollingUpgradePolicy\": false,\r\n            \"disableAutomaticRollback\": false\r\n          }\r\n        },\r\n        \"virtualMachineProfile\": {\r\n          \"osProfile\": {\r\n            \"computerNamePrefix\": \"vmss\",\r\n            \"adminUsername\": \"sample-user\",\r\n            \"windowsConfiguration\": {\r\n              \"provisionVMAgent\": true,\r\n              \"enableAutomaticUpdates\": true,\r\n              \"enableVMAgentPlatformUpdates\": false\r\n            },\r\n            \"secrets\": [],\r\n            \"allowExtensionOperations\": true,\r\n            \"requireGuestProvisionSignal\": true\r\n          },\r\n          \"storageProfile\": {\r\n            \"osDisk\": {\r\n              \"osType\": \"Windows\",\r\n              \"createOption\": \"FromImage\",\r\n              \"caching\": \"None\",\r\n              \"managedDisk\": {\r\n                \"storageAccountType\": \"Premium_LRS\"\r\n              },\r\n              \"diskSizeGB\": 127\r\n            },\r\n            \"imageReference\": {\r\n              \"publisher\": \"MicrosoftWindowsServer\",\r\n              \"offer\": \"WindowsServer\",\r\n              \"sku\": \"2019-Datacenter\",\r\n              \"version\": \"latest\"\r\n            }\r\n          },\r\n          \"networkProfile\": {\"networkInterfaceConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"primary\":true,\"enableAcceleratedNetworking\":false,\"disableTcpStateTracking\":false,\"dnsSettings\":{\"dnsServers\":[]},\"enableIPForwarding\":true,\"ipConfigurations\":[{\"name\":\"testResource\",\"properties\":{\"subnet\":{\"id\":\"/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1/subnets/subnet1\"},\"privateIPAddressVersion\":\"IPv4\"}}]}}]},\r\n          \"extensionProfile\": {\r\n            \"extensions\": [\r\n              {\r\n                \"name\": \"Microsoft.Azure.Security.AntimalwareSignature.AntimalwareConfiguration\",\r\n                \"properties\": {\r\n                  \"autoUpgradeMinorVersion\": true,\r\n                  \"enableAutomaticUpgrade\": true,\r\n                  \"publisher\": \"Microsoft.Azure.Security.AntimalwareSignature\",\r\n                  \"type\": \"AntimalwareConfiguration\",\r\n                  \"typeHandlerVersion\": \"2.0\",\r\n                  \"settings\": {}\r\n                }\r\n              },\r\n              {\r\n                \"name\": \"Microsoft.Azure.Geneva.GenevaMonitoring\",\r\n                \"properties\": {\r\n                  \"autoUpgradeMinorVersion\": true,\r\n                  \"enableAutomaticUpgrade\": true,\r\n                  \"publisher\": \"Microsoft.Azure.Geneva\",\r\n                  \"type\": \"GenevaMonitoring\",\r\n                  \"typeHandlerVersion\": \"2.0\"\r\n                }\r\n              }\r\n            ]\r\n          },\r\n          \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n        },\r\n        \"provisioningState\": \"Succeeded\",\r\n        \"overprovision\": false,\r\n        \"doNotRunExtensionsOnOverprovisionedVMs\": false,\r\n        \"uniqueId\": \"********-0000-0000-0000-************\",\r\n        \"platformFaultDomainCount\": 5,\r\n        \"timeCreated\": \"2001-02-03T04:05:06Z\"\r\n      }\r\n    }\r\n  ]\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "3940"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 13
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSetnotfound/providers/Microsoft.Compute/virtualMachineScaleSets?api-version=2024-07-01
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 130
        uncompressed: false
        body: '{"error":{"code":"ResourceGroupNotFound","message":"Resource group ''aks-cit-VirtualMachineScaleSetnotfound'' could not be found."}}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "130"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Failure-Cause:
                - gateway
        status: 404 Not Found
        code: 404
        duration: 200ms
    - id: 14
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Compute/virtualMachineScaleSets/testResource?api-version=2024-07-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&p=********-0000-0000-0000-************&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&monitor=true&p=********-0000-0000-0000-************&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
            X-Ms-Request-Charge:
                - "1"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 15
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-VirtualMachineScaleS azsdk-go-armcompute/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Compute/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-07-01&c=c&h=h&p=********-0000-0000-0000-************&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 184
        uncompressed: false
        body: "{\r\n  \"startTime\": \"2001-02-03T04:05:06Z\",\r\n  \"endTime\": \"2001-02-03T04:05:06Z\",\r\n  \"status\": \"Succeeded\",\r\n  \"name\": \"********-0000-0000-0000-************\"\r\n}"
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "184"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Need-To-Refresh-Epl-Cache:
                - "False"
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 16
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourceGroups/aks-cit-VirtualMachineScaleSet/providers/Microsoft.Network/virtualNetworks/vnet1?api-version=2024-05-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Azure-Asyncnotification:
                - Enabled
            Azure-Asyncoperation:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Network/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Network/locations/eastus/operationResults/********-0000-0000-0000-************?api-version=2024-05-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 17
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - azsdk-go-armnetwork/v6.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/providers/Microsoft.Network/locations/eastus/operations/********-0000-0000-0000-************?api-version=2024-05-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 22
        uncompressed: false
        body: '{"status":"Succeeded"}'
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "22"
            Content-Type:
                - application/json; charset=utf-8
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
    - id: 18
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            Accept:
                - application/json
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/resourcegroups/aks-cit-VirtualMachineScaleSet?api-version=2021-04-01
        method: DELETE
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Location:
                - https://management.azure.com/subscriptions/********-0000-0000-0000-************/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRWSVJUVUFMTUFDSElORVNDQUxFU0VULUVBU1RVUyIsImpvYkxvY2F0aW9uIjoiZWFzdHVzIn0?api-version=2021-04-01&c=c&h=h&s=s&t=t
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Deletes:
                - "11999"
        status: 202 Accepted
        code: 202
        duration: 200ms
    - id: 19
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 0
        transfer_encoding: []
        trailer: {}
        host: management.azure.com
        remote_addr: ""
        request_uri: ""
        body: ""
        form: {}
        headers:
            User-Agent:
                - ccm-resource-group-clien azsdk-go-armresources/v1.2.0 (go1.23.2; linux)
        url: https://management.azure.com/subscriptions/********-0000-0000-0000-************/operationresults/eyJqb2JJZCI6IlJFU09VUkNFR1JPVVBERUxFVElPTkpPQi1BS1M6MkRDSVQ6MkRWSVJUVUFMTUFDSElORVNDQUxFU0VULUVBU1RVUyIsImpvYkxvY2F0aW9uIjoiZWFzdHVzIn0?api-version=2021-04-01&c=c&h=h&s=s&t=t
        method: GET
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        transfer_encoding: []
        trailer: {}
        content_length: 0
        uncompressed: false
        body: ""
        headers:
            Cache-Control:
                - no-cache
            Content-Length:
                - "0"
            Expires:
                - "-1"
            Pragma:
                - no-cache
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains
            X-Cache:
                - CONFIG_NOCACHE
            X-Content-Type-Options:
                - nosniff
            X-Ms-Ratelimit-Remaining-Subscription-Global-Reads:
                - "16499"
        status: 200 OK
        code: 200
        duration: 200ms
