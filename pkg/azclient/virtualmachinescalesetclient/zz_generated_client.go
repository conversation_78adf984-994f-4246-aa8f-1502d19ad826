// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package virtualmachinescalesetclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/arm"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/tracing"
	armcompute "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v6"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/metrics"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/policy/etag"
	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

const AzureStackCloudAPIVersion = "2019-07-01"

type Client struct {
	*armcompute.VirtualMachineScaleSetsClient
	subscriptionID string
	tracer         tracing.Tracer
}

func New(subscriptionID string, credential azcore.TokenCredential, options *arm.ClientOptions) (Interface, error) {
	if options == nil {
		options = utils.GetDefaultOption()
	}
	tr := options.TracingProvider.NewTracer(utils.ModuleName, utils.ModuleVersion)

	options.ClientOptions.PerCallPolicies = append(options.ClientOptions.PerCallPolicies, utils.FuncPolicyWrapper(etag.AppendEtag))
	client, err := armcompute.NewVirtualMachineScaleSetsClient(subscriptionID, credential, options)
	if err != nil {
		return nil, err
	}
	return &Client{
		VirtualMachineScaleSetsClient: client,
		subscriptionID:                subscriptionID,
		tracer:                        tr,
	}, nil
}

const CreateOrUpdateOperationName = "VirtualMachineScaleSetsClient.Create"

// CreateOrUpdate creates or updates a VirtualMachineScaleSet.
func (client *Client) CreateOrUpdate(ctx context.Context, resourceGroupName string, virtualmachinescalesetName string, resource armcompute.VirtualMachineScaleSet) (result *armcompute.VirtualMachineScaleSet, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "VirtualMachineScaleSet", "create_or_update")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, CreateOrUpdateOperationName, client.tracer, nil)
	defer endSpan(err)
	resp, err := utils.NewPollerWrapper(client.VirtualMachineScaleSetsClient.BeginCreateOrUpdate(ctx, resourceGroupName, virtualmachinescalesetName, resource, nil)).WaitforPollerResp(ctx)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &resp.VirtualMachineScaleSet, nil
	}
	return nil, nil
}

const DeleteOperationName = "VirtualMachineScaleSetsClient.Delete"

// Delete deletes a VirtualMachineScaleSet by name.
func (client *Client) Delete(ctx context.Context, resourceGroupName string, virtualmachinescalesetName string) (err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "VirtualMachineScaleSet", "delete")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, DeleteOperationName, client.tracer, nil)
	defer endSpan(err)
	_, err = utils.NewPollerWrapper(client.BeginDelete(ctx, resourceGroupName, virtualmachinescalesetName, nil)).WaitforPollerResp(ctx)
	return err
}

const ListOperationName = "VirtualMachineScaleSetsClient.List"

// List gets a list of VirtualMachineScaleSet in the resource group.
func (client *Client) List(ctx context.Context, resourceGroupName string) (result []*armcompute.VirtualMachineScaleSet, err error) {
	metricsCtx := metrics.BeginARMRequest(client.subscriptionID, resourceGroupName, "VirtualMachineScaleSet", "list")
	defer func() { metricsCtx.Observe(ctx, err) }()
	ctx, endSpan := runtime.StartSpan(ctx, ListOperationName, client.tracer, nil)
	defer endSpan(err)
	pager := client.VirtualMachineScaleSetsClient.NewListPager(resourceGroupName, nil)
	for pager.More() {
		nextResult, err := pager.NextPage(ctx)
		if err != nil {
			return nil, err
		}
		result = append(result, nextResult.Value...)
	}
	return result, nil
}
