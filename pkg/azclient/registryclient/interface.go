/*
Copyright 2023 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// +azure:enableclientgen:=true
package registryclient

import (
	"context"

	armcontainerregistry "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/containerregistry/armcontainerregistry"

	"sigs.k8s.io/cloud-provider-azure/pkg/azclient/utils"
)

// +azure:client:verbs=get;delete;listbyrg,resource=Registry,packageName=github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/containerregistry/armcontainerregistry,packageAlias=armcontainerregistry,clientName=RegistriesClient,expand=false,mooncakeApiVersion="2023-07-01"
type Interface interface {
	utils.GetFunc[armcontainerregistry.Registry]
	Create(ctx context.Context, resourceGroupName string, resourceName string, resourceParam armcontainerregistry.Registry) (*armcontainerregistry.Registry, error)
	utils.DeleteFunc[armcontainerregistry.Registry]
	utils.ListFunc[armcontainerregistry.Registry]
	ImportImage(ctx context.Context, resourceGroup string, resourceName string, param armcontainerregistry.ImportImageParameters) error
}
