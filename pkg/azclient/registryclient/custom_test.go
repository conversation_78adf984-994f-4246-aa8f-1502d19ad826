// /*
// Copyright The Kubernetes Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by client-gen. DO NOT EDIT.
package registryclient

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	armcontainerregistry "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/containerregistry/armcontainerregistry"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

func init() {
	additionalTestCases = func() {
		When("create registry with location", func() {
			It("should return without error", func() {
				registry, err := realClient.Create(context.Background(), resourceGroupName, resourceName, armcontainerregistry.Registry{
					Location: to.Ptr(location),
					SKU: &armcontainerregistry.SKU{
						Name: to.Ptr(armcontainerregistry.SKUNameStandard),
					},
				})
				Expect(err).NotTo(HaveOccurred())
				Expect(*registry.Name).To(Equal(resourceName))
			})
		})
		When("import image from public repo", func() {
			It("should return without error", func() {
				imageHost := "registry.hub.docker.com"
				if location == "chinaeast2" {
					imageHost = "dockerhub.azk8s.cn"
				}
				err := realClient.ImportImage(context.Background(), resourceGroupName, resourceName, armcontainerregistry.ImportImageParameters{
					Source: &armcontainerregistry.ImportSource{
						RegistryURI: to.Ptr(imageHost),
						SourceImage: to.Ptr("tensorflow/tensorflow:latest-gpu"),
					},
					TargetTags: []*string{to.Ptr("tensorflow:latest-gpu")},
				})
				Expect(err).NotTo(HaveOccurred())
			})
		})
	}

	beforeAllFunc = func(ctx context.Context) {
		resourceName = "akscittestregistryfixed"
	}
	afterAllFunc = func(ctx context.Context) {
	}
}
